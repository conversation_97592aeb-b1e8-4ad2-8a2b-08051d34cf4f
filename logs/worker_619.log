nohup: ignoring input
[2025-07-29 17:12:25,407: DEBUG/MainProcess] | Worker: Preparing bootsteps.
[2025-07-29 17:12:25,409: DEBUG/MainProcess] | Worker: Building graph...
[2025-07-29 17:12:25,409: DEBUG/MainProcess] | Worker: New boot order: {StateDB, Beat, Timer, Hub, Pool, Autoscaler, Consumer}
[2025-07-29 17:12:25,414: DEBUG/MainProcess] | Consumer: Preparing bootsteps.
[2025-07-29 17:12:25,414: DEBUG/MainProcess] | Consumer: Building graph...
[2025-07-29 17:12:25,425: DEBUG/MainProcess] | Consumer: New boot order: {Connection, Agent, Events, Heart, Mingle, Tasks, Control, Gossip, event loop}
 
 -------------- celery@workstation v5.4.0 (opalescent)
--- ***** ----- 
-- ******* ---- Linux-6.12.10-76061203-generic-x86_64-with-glibc2.35 2025-07-29 17:12:25
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         analysis_tasks:0x7736c7ac06e0
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 12 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                .> general_queue    exchange=general_queue(direct) key=general_queue
                .> mcc_queue        exchange=mcc_queue(direct) key=mcc_queue
                .> policy_queue     exchange=policy_queue(direct) key=policy_queue
                .> risky_queue      exchange=risky_queue(direct) key=risky_queue

[tasks]
  . celery.accumulate
  . celery.backend_cleanup
  . celery.chain
  . celery.chord
  . celery.chord_unlock
  . celery.chunks
  . celery.group
  . celery.map
  . celery.starmap
  . process_policy_analysis
  . process_policy_analysis_enhanced
  . test_task

[2025-07-29 17:12:25,439: DEBUG/MainProcess] | Worker: Starting Hub
[2025-07-29 17:12:25,439: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:12:25,439: DEBUG/MainProcess] | Worker: Starting Pool
[2025-07-29 17:12:27,268: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:12:27,268: DEBUG/MainProcess] | Worker: Starting Consumer
[2025-07-29 17:12:27,268: DEBUG/MainProcess] | Consumer: Starting Connection
[2025-07-29 17:12:27,285: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-29 17:12:27,285: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:12:27,285: DEBUG/MainProcess] | Consumer: Starting Events
[2025-07-29 17:12:27,286: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:12:27,286: DEBUG/MainProcess] | Consumer: Starting Heart
[2025-07-29 17:12:27,288: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:12:27,288: DEBUG/MainProcess] | Consumer: Starting Mingle
[2025-07-29 17:12:27,288: INFO/MainProcess] mingle: searching for neighbors
[2025-07-29 17:12:28,299: INFO/MainProcess] mingle: all alone
[2025-07-29 17:12:28,299: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:12:28,300: DEBUG/MainProcess] | Consumer: Starting Tasks
[2025-07-29 17:12:28,326: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:12:28,326: DEBUG/MainProcess] | Consumer: Starting Control
[2025-07-29 17:12:28,330: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:12:28,330: DEBUG/MainProcess] | Consumer: Starting Gossip
[2025-07-29 17:12:28,334: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:12:28,334: DEBUG/MainProcess] | Consumer: Starting event loop
[2025-07-29 17:12:28,334: DEBUG/MainProcess] | Worker: Hub.register Pool...
[2025-07-29 17:12:28,334: INFO/MainProcess] celery@workstation ready.
[2025-07-29 17:12:28,335: DEBUG/MainProcess] basic.qos: prefetch_count->12
[2025-07-29 17:13:29,808: INFO/MainProcess] Task process_policy_analysis_enhanced[2f8bfe0c-b419-4f1c-9faf-6379fad27bcb] received
[2025-07-29 17:13:29,808: DEBUG/MainProcess] TaskPool: Apply <function fast_trace_task at 0x7736c8b64ae0> (args:('process_policy_analysis_enhanced', '2f8bfe0c-b419-4f1c-9faf-6379fad27bcb', {'lang': 'py', 'task': 'process_policy_analysis_enhanced', 'id': '2f8bfe0c-b419-4f1c-9faf-6379fad27bcb', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [2400, 2100], 'root_id': '2f8bfe0c-b419-4f1c-9faf-6379fad27bcb', 'parent_id': None, 'argsrepr': '(1,)', 'kwargsrepr': '{}', 'origin': 'gen6061@workstation', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'properties': {'correlation_id': '2f8bfe0c-b419-4f1c-9faf-6379fad27bcb', 'reply_to': '********-eca7-30d6-bba6-896bfb0840f6', 'delivery_mode': 2, 'delivery_info': {'exchange': '', 'routing_key': 'policy_queue'}, 'priority': 0, 'body_encoding': 'base64', 'delivery_tag': '********-9b06-44c0-a311-407a11b51fe6'}, 'reply_to': '********-eca7-30d6-bba6-896bfb0840f6', 'correlation_id': '2f8bfe0c-b419-4f1c-9faf-6379fad27bcb', 'hostname': 'celery@workstation', 'delivery_info': {'exchange':... kwargs:{})
2025-07-29 17:13:29,821 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-29 17:13:29,821: INFO/ForkPoolWorker-7] BEGIN (implicit)
2025-07-29 17:13:29,854 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
[2025-07-29 17:13:29,854: INFO/ForkPoolWorker-7] SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:13:29,855 INFO sqlalchemy.engine.Engine [generated in 0.00062s] (1,)
[2025-07-29 17:13:29,855: INFO/ForkPoolWorker-7] [generated in 0.00062s] (1,)
2025-07-29 17:13:29,859 INFO sqlalchemy.engine.Engine ROLLBACK
[2025-07-29 17:13:29,859: INFO/ForkPoolWorker-7] ROLLBACK
[2025-07-29 17:13:29,859: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][process_cleanup][NO_REF] DEBUG: Exit cleanup registered
[2025-07-29 17:13:29,859: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][1][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Starting enhanced policy analysis task for analysis_id: 1
[2025-07-29 17:13:29,860: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][1][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Importing required modules for enhanced policy analysis
[2025-07-29 17:13:29,860: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][1][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Opening database session to get PolicyAnalysisNew record
2025-07-29 17:13:29,861 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-29 17:13:29,861: INFO/ForkPoolWorker-7] BEGIN (implicit)
2025-07-29 17:13:29,862 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
[2025-07-29 17:13:29,862: INFO/ForkPoolWorker-7] SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:13:29,862 INFO sqlalchemy.engine.Engine [cached since 0.007803s ago] (1,)
[2025-07-29 17:13:29,862: INFO/ForkPoolWorker-7] [cached since 0.007803s ago] (1,)
[2025-07-29 17:13:29,863: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][1][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Found PolicyAnalysisNew record: https://devomkids.com/, ref_id: cffbff75-39a0-455a-97e2-609dcef74a90
2025-07-29 17:13:29,867 INFO sqlalchemy.engine.Engine UPDATE policy_analysis_new_gemini SET started_at=?, processing_status=? WHERE policy_analysis_new_gemini.id = ?
[2025-07-29 17:13:29,867: INFO/ForkPoolWorker-7] UPDATE policy_analysis_new_gemini SET started_at=?, processing_status=? WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:13:29,867 INFO sqlalchemy.engine.Engine [generated in 0.00040s] ('2025-07-29T17:13:29.863572Z', 'PROCESSING', 1)
[2025-07-29 17:13:29,867: INFO/ForkPoolWorker-7] [generated in 0.00040s] ('2025-07-29T17:13:29.863572Z', 'PROCESSING', 1)
2025-07-29 17:13:29,868 INFO sqlalchemy.engine.Engine COMMIT
[2025-07-29 17:13:29,868: INFO/ForkPoolWorker-7] COMMIT
[2025-07-29 17:13:29,871: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][1][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Updated PolicyAnalysisNew status to PROCESSING
[2025-07-29 17:13:29,871: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][1][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Initializing Enhanced Policy Analysis service
[2025-07-29 17:13:29,871: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Enhanced Policy Analysis Service initialized
{
  "scrape_request_ref_id": "cffbff75-39a0-455a-97e2-609dcef74a90",
  "org_id": "default",
  "required_categories": 6,
  "social_media_categories": 7
}
[2025-07-29 17:13:29,871: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][1][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Running Enhanced Policy Analysis with conditional popup handling
[2025-07-29 17:13:29,872: DEBUG/ForkPoolWorker-7] Using selector: EpollSelector
[2025-07-29 17:13:29,872: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ================================================================================
[2025-07-29 17:13:29,873: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🚀 ENHANCED POLICY ANALYSIS STARTED (UNIFIED APPROACH)
[2025-07-29 17:13:29,873: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ================================================================================
[2025-07-29 17:13:29,873: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 📋 Request ID: cffbff75-39a0-455a-97e2-609dcef74a90
[2025-07-29 17:13:29,873: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🏢 Organization: default
[2025-07-29 17:13:29,873: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ================================================================================
[2025-07-29 17:13:29,873: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 
[2025-07-29 17:13:29,873: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 📋 STEP 1: RETRIEVING URLs FROM DATABASE
[2025-07-29 17:13:29,873: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: --------------------------------------------------
2025-07-29 17:13:29,874 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-29 17:13:29,874: INFO/ForkPoolWorker-7] BEGIN (implicit)
2025-07-29 17:13:29,877 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = ?
[2025-07-29 17:13:29,877: INFO/ForkPoolWorker-7] SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = ?
2025-07-29 17:13:29,877 INFO sqlalchemy.engine.Engine [generated in 0.00057s] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
[2025-07-29 17:13:29,877: INFO/ForkPoolWorker-7] [generated in 0.00057s] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
2025-07-29 17:13:29,881 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
[2025-07-29 17:13:29,881: INFO/ForkPoolWorker-7] SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 17:13:29,882 INFO sqlalchemy.engine.Engine [generated in 0.00045s] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
[2025-07-29 17:13:29,882: INFO/ForkPoolWorker-7] [generated in 0.00045s] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
2025-07-29 17:13:29,884 INFO sqlalchemy.engine.Engine ROLLBACK
[2025-07-29 17:13:29,884: INFO/ForkPoolWorker-7] ROLLBACK
[2025-07-29 17:13:29,884: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ RESULT: URLs successfully retrieved
[2025-07-29 17:13:29,884: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📊 Website: https://devomkids.com/
[2025-07-29 17:13:29,884: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📊 Depth 1 URLs: 49
[2025-07-29 17:13:29,885: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📊 Depth 2 URLs: 0
[2025-07-29 17:13:29,885: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📊 Total URLs: 49
[2025-07-29 17:13:29,885: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 
[2025-07-29 17:13:29,885: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🏷️ STEP 2: CLASSIFYING URLs (UNIFIED SOFT → HARD)
[2025-07-29 17:13:29,885: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: --------------------------------------------------
[2025-07-29 17:13:29,885: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting URL classification using proper service methods
[2025-07-29 17:13:29,885: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting soft URL classification
[2025-07-29 17:13:29,885: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:29][test-analysis][NO_REF] INFO: Starting URL processing for model policy result
{
  "website": "https://devomkids.com/",
  "total_urls": 49
}
[2025-07-29 17:13:29,889: DEBUG/ForkPoolWorker-7] Starting new HTTPS connection (1): openaipublic.blob.core.windows.net:443
[2025-07-29 17:13:31,433: DEBUG/ForkPoolWorker-7] https://openaipublic.blob.core.windows.net:443 "GET /encodings/cl100k_base.tiktoken HTTP/1.1" 200 1681126
[2025-07-29 17:13:34,270: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][test-analysis][NO_REF] INFO: Token calculation: system=265, base_user=988, available_for_urls=87747
[2025-07-29 17:13:34,271: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][test-analysis][NO_REF] INFO: Total URL tokens: 885, Available: 87747
[2025-07-29 17:13:34,271: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][test-analysis][NO_REF] INFO: All URLs fit within token limit
[2025-07-29 17:13:34,271: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][test-analysis][NO_REF] INFO: Final URLs after token limiting
{
  "original_url_count": 49,
  "final_url_count": 49,
  "urls_trimmed": 0,
  "system_tokens": 265,
  "base_user_tokens": 988,
  "url_tokens": 885,
  "final_total_tokens": 2138,
  "token_limit": 90000,
  "remaining_tokens": 87862
}
[2025-07-29 17:13:34,272: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][test-analysis][NO_REF] INFO: Final prompt verification
{
  "actual_prompt_tokens": 2380,
  "token_limit": 90000,
  "within_limit": true,
  "processing_time": 4.387154817581177
}
[2025-07-29 17:13:34,272: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] ERROR: Error in soft URL classification: get_optimized_gemini_response_for_task() got an unexpected keyword argument 'model_name'
[2025-07-29 17:13:34,276: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] ERROR: ❌ ERROR: URL classification failed
[2025-07-29 17:13:34,276: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][1][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Enhanced Policy Analysis task completed in 4.46 seconds with status: FAILED
2025-07-29 17:13:34,277 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-29 17:13:34,277: INFO/ForkPoolWorker-7] BEGIN (implicit)
2025-07-29 17:13:34,277 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
[2025-07-29 17:13:34,277: INFO/ForkPoolWorker-7] SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:13:34,277 INFO sqlalchemy.engine.Engine [cached since 4.423s ago] (1,)
[2025-07-29 17:13:34,277: INFO/ForkPoolWorker-7] [cached since 4.423s ago] (1,)
2025-07-29 17:13:34,278 INFO sqlalchemy.engine.Engine UPDATE policy_analysis_new_gemini SET completed_at=?, error_message=?, processing_status=? WHERE policy_analysis_new_gemini.id = ?
[2025-07-29 17:13:34,278: INFO/ForkPoolWorker-7] UPDATE policy_analysis_new_gemini SET completed_at=?, error_message=?, processing_status=? WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:13:34,278 INFO sqlalchemy.engine.Engine [generated in 0.00017s] ('2025-07-29T17:13:34.277713Z', 'URL classification failed', 'FAILED', 1)
[2025-07-29 17:13:34,278: INFO/ForkPoolWorker-7] [generated in 0.00017s] ('2025-07-29T17:13:34.277713Z', 'URL classification failed', 'FAILED', 1)
2025-07-29 17:13:34,278 INFO sqlalchemy.engine.Engine COMMIT
[2025-07-29 17:13:34,278: INFO/ForkPoolWorker-7] COMMIT
[2025-07-29 17:13:34,281: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][1][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Updated PolicyAnalysisNew record with results
[2025-07-29 17:13:34,281: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][process_cleanup][NO_REF] INFO: Running Celery task cleanup
[2025-07-29 17:13:34,319: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][process_cleanup][NO_REF] INFO: Found 20 Playwright processes to clean up
[2025-07-29 17:13:34,319: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][process_cleanup][NO_REF] INFO: Gracefully terminated process 5870
[2025-07-29 17:13:34,371: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][process_cleanup][NO_REF] INFO: Gracefully terminated process 5871
[2025-07-29 17:13:34,424: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][process_cleanup][NO_REF] INFO: Gracefully terminated process 6243
[2025-07-29 17:13:34,437: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][process_cleanup][NO_REF] INFO: Gracefully terminated process 6389
[2025-07-29 17:13:34,451: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][process_cleanup][NO_REF] INFO: Gracefully terminated process 6395
[2025-07-29 17:13:34,704: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][process_cleanup][NO_REF] INFO: Gracefully terminated process 7933
[2025-07-29 17:13:34,718: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][process_cleanup][NO_REF] INFO: Gracefully terminated process 7943
[2025-07-29 17:13:34,718: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][process_cleanup][NO_REF] WARNING: Could not clean up async tasks: There is no current event loop in thread 'MainThread'.
[2025-07-29 17:13:34,718: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][process_cleanup][NO_REF] INFO: Celery task cleanup completed
[2025-07-29 17:13:34,761: WARNING/ForkPoolWorker-7] [2025-07-29 17:13:34][process_cleanup][NO_REF] DEBUG: No Playwright processes found to clean up
[2025-07-29 17:13:34,762: INFO/ForkPoolWorker-7] Task process_policy_analysis_enhanced[2f8bfe0c-b419-4f1c-9faf-6379fad27bcb] succeeded in 4.952577738999992s: {'status': 'failed', 'task_id': '2f8bfe0c-b419-4f1c-9faf-6379fad27bcb', 'analysis_id': 1, 'execution_time': 4.460237264633179}
