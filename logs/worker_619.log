nohup: ignoring input
[2025-07-29 17:30:49,668: DEBUG/MainProcess] | Worker: Preparing bootsteps.
[2025-07-29 17:30:49,669: DEBUG/MainProcess] | Worker: Building graph...
[2025-07-29 17:30:49,670: DEBUG/MainProcess] | Worker: New boot order: {<PERSON>, Timer, Hub, Pool, Autoscaler, StateDB, Consumer}
[2025-07-29 17:30:49,673: DEBUG/MainProcess] | Consumer: Preparing bootsteps.
[2025-07-29 17:30:49,673: DEBUG/MainProcess] | Consumer: Building graph...
[2025-07-29 17:30:49,681: DEBUG/MainProcess] | Consumer: New boot order: {Connection, Events, Mingle, Tasks, Control, Heart, Agent, Gossip, event loop}
 
 -------------- celery@workstation v5.4.0 (opalescent)
--- ***** ----- 
-- ******* ---- Linux-6.12.10-76061203-generic-x86_64-with-glibc2.35 2025-07-29 17:30:49
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         analysis_tasks:0x79b7638b06e0
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 12 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                .> general_queue    exchange=general_queue(direct) key=general_queue
                .> mcc_queue        exchange=mcc_queue(direct) key=mcc_queue
                .> policy_queue     exchange=policy_queue(direct) key=policy_queue
                .> risky_queue      exchange=risky_queue(direct) key=risky_queue

[tasks]
  . celery.accumulate
  . celery.backend_cleanup
  . celery.chain
  . celery.chord
  . celery.chord_unlock
  . celery.chunks
  . celery.group
  . celery.map
  . celery.starmap
  . process_policy_analysis
  . process_policy_analysis_enhanced
  . test_task

[2025-07-29 17:30:49,701: DEBUG/MainProcess] | Worker: Starting Hub
[2025-07-29 17:30:49,701: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:30:49,701: DEBUG/MainProcess] | Worker: Starting Pool
[2025-07-29 17:30:51,569: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:30:51,569: DEBUG/MainProcess] | Worker: Starting Consumer
[2025-07-29 17:30:51,569: DEBUG/MainProcess] | Consumer: Starting Connection
[2025-07-29 17:30:51,586: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-29 17:30:51,586: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:30:51,586: DEBUG/MainProcess] | Consumer: Starting Events
[2025-07-29 17:30:51,587: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:30:51,587: DEBUG/MainProcess] | Consumer: Starting Mingle
[2025-07-29 17:30:51,587: INFO/MainProcess] mingle: searching for neighbors
[2025-07-29 17:30:52,593: INFO/MainProcess] mingle: all alone
[2025-07-29 17:30:52,593: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:30:52,593: DEBUG/MainProcess] | Consumer: Starting Tasks
[2025-07-29 17:30:52,596: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:30:52,596: DEBUG/MainProcess] | Consumer: Starting Control
[2025-07-29 17:30:52,598: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:30:52,598: DEBUG/MainProcess] | Consumer: Starting Heart
[2025-07-29 17:30:52,599: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:30:52,599: DEBUG/MainProcess] | Consumer: Starting Gossip
[2025-07-29 17:30:52,601: DEBUG/MainProcess] ^-- substep ok
[2025-07-29 17:30:52,601: DEBUG/MainProcess] | Consumer: Starting event loop
[2025-07-29 17:30:52,601: DEBUG/MainProcess] | Worker: Hub.register Pool...
[2025-07-29 17:30:52,601: INFO/MainProcess] celery@workstation ready.
[2025-07-29 17:30:52,601: DEBUG/MainProcess] basic.qos: prefetch_count->12
[2025-07-29 17:34:28,141: INFO/MainProcess] Task process_policy_analysis_enhanced[c671c51c-99e7-45ef-b651-bee2092d8329] received
[2025-07-29 17:34:28,141: DEBUG/MainProcess] TaskPool: Apply <function fast_trace_task at 0x79b764960ae0> (args:('process_policy_analysis_enhanced', 'c671c51c-99e7-45ef-b651-bee2092d8329', {'lang': 'py', 'task': 'process_policy_analysis_enhanced', 'id': 'c671c51c-99e7-45ef-b651-bee2092d8329', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [2400, 2100], 'root_id': 'c671c51c-99e7-45ef-b651-bee2092d8329', 'parent_id': None, 'argsrepr': '(2,)', 'kwargsrepr': '{}', 'origin': 'gen10375@workstation', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'properties': {'correlation_id': 'c671c51c-99e7-45ef-b651-bee2092d8329', 'reply_to': 'e3c2a5cb-9962-3173-9b37-a1008c1c0896', 'delivery_mode': 2, 'delivery_info': {'exchange': '', 'routing_key': 'policy_queue'}, 'priority': 0, 'body_encoding': 'base64', 'delivery_tag': '6a31d680-ff05-4836-9e08-8dc487942b09'}, 'reply_to': 'e3c2a5cb-9962-3173-9b37-a1008c1c0896', 'correlation_id': 'c671c51c-99e7-45ef-b651-bee2092d8329', 'hostname': 'celery@workstation', 'delivery_info': {'exchange':... kwargs:{})
2025-07-29 17:34:28,150 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-29 17:34:28,150: INFO/ForkPoolWorker-7] BEGIN (implicit)
2025-07-29 17:34:28,172 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
[2025-07-29 17:34:28,172: INFO/ForkPoolWorker-7] SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:34:28,172 INFO sqlalchemy.engine.Engine [generated in 0.00050s] (2,)
[2025-07-29 17:34:28,172: INFO/ForkPoolWorker-7] [generated in 0.00050s] (2,)
2025-07-29 17:34:28,175 INFO sqlalchemy.engine.Engine ROLLBACK
[2025-07-29 17:34:28,175: INFO/ForkPoolWorker-7] ROLLBACK
[2025-07-29 17:34:28,175: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][process_cleanup][NO_REF] DEBUG: Exit cleanup registered
[2025-07-29 17:34:28,175: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][2][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Starting enhanced policy analysis task for analysis_id: 2
[2025-07-29 17:34:28,175: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][2][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Importing required modules for enhanced policy analysis
[2025-07-29 17:34:28,175: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][2][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Opening database session to get PolicyAnalysisNew record
2025-07-29 17:34:28,176 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-29 17:34:28,176: INFO/ForkPoolWorker-7] BEGIN (implicit)
2025-07-29 17:34:28,177 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
[2025-07-29 17:34:28,177: INFO/ForkPoolWorker-7] SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:34:28,177 INFO sqlalchemy.engine.Engine [cached since 0.005232s ago] (2,)
[2025-07-29 17:34:28,177: INFO/ForkPoolWorker-7] [cached since 0.005232s ago] (2,)
[2025-07-29 17:34:28,177: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][2][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Found PolicyAnalysisNew record: https://devomkids.com/, ref_id: cffbff75-39a0-455a-97e2-609dcef74a90
2025-07-29 17:34:28,180 INFO sqlalchemy.engine.Engine UPDATE policy_analysis_new_gemini SET started_at=?, processing_status=? WHERE policy_analysis_new_gemini.id = ?
[2025-07-29 17:34:28,180: INFO/ForkPoolWorker-7] UPDATE policy_analysis_new_gemini SET started_at=?, processing_status=? WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:34:28,180 INFO sqlalchemy.engine.Engine [generated in 0.00022s] ('2025-07-29T17:34:28.177976Z', 'PROCESSING', 2)
[2025-07-29 17:34:28,180: INFO/ForkPoolWorker-7] [generated in 0.00022s] ('2025-07-29T17:34:28.177976Z', 'PROCESSING', 2)
2025-07-29 17:34:28,181 INFO sqlalchemy.engine.Engine COMMIT
[2025-07-29 17:34:28,181: INFO/ForkPoolWorker-7] COMMIT
[2025-07-29 17:34:28,183: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][2][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Updated PolicyAnalysisNew status to PROCESSING
[2025-07-29 17:34:28,183: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][2][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Initializing Enhanced Policy Analysis service
[2025-07-29 17:34:28,184: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Enhanced Policy Analysis Service initialized
{
  "scrape_request_ref_id": "cffbff75-39a0-455a-97e2-609dcef74a90",
  "org_id": "default",
  "required_categories": 6,
  "social_media_categories": 7
}
[2025-07-29 17:34:28,184: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][2][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Running Enhanced Policy Analysis with conditional popup handling
[2025-07-29 17:34:28,184: DEBUG/ForkPoolWorker-7] Using selector: EpollSelector
[2025-07-29 17:34:28,185: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ================================================================================
[2025-07-29 17:34:28,185: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🚀 ENHANCED POLICY ANALYSIS STARTED (UNIFIED APPROACH)
[2025-07-29 17:34:28,185: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ================================================================================
[2025-07-29 17:34:28,185: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 📋 Request ID: cffbff75-39a0-455a-97e2-609dcef74a90
[2025-07-29 17:34:28,185: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🏢 Organization: default
[2025-07-29 17:34:28,185: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ================================================================================
[2025-07-29 17:34:28,185: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 
[2025-07-29 17:34:28,185: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 📋 STEP 1: RETRIEVING URLs FROM DATABASE
[2025-07-29 17:34:28,185: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: --------------------------------------------------
2025-07-29 17:34:28,186 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-29 17:34:28,186: INFO/ForkPoolWorker-7] BEGIN (implicit)
2025-07-29 17:34:28,188 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = ?
[2025-07-29 17:34:28,188: INFO/ForkPoolWorker-7] SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = ?
2025-07-29 17:34:28,188 INFO sqlalchemy.engine.Engine [generated in 0.00028s] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
[2025-07-29 17:34:28,188: INFO/ForkPoolWorker-7] [generated in 0.00028s] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
2025-07-29 17:34:28,191 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
[2025-07-29 17:34:28,191: INFO/ForkPoolWorker-7] SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 17:34:28,191 INFO sqlalchemy.engine.Engine [generated in 0.00022s] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
[2025-07-29 17:34:28,191: INFO/ForkPoolWorker-7] [generated in 0.00022s] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
2025-07-29 17:34:28,192 INFO sqlalchemy.engine.Engine ROLLBACK
[2025-07-29 17:34:28,192: INFO/ForkPoolWorker-7] ROLLBACK
[2025-07-29 17:34:28,193: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ RESULT: URLs successfully retrieved
[2025-07-29 17:34:28,193: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📊 Website: https://devomkids.com/
[2025-07-29 17:34:28,193: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📊 Depth 1 URLs: 49
[2025-07-29 17:34:28,193: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📊 Depth 2 URLs: 0
[2025-07-29 17:34:28,193: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📊 Total URLs: 49
[2025-07-29 17:34:28,193: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 
[2025-07-29 17:34:28,193: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🏷️ STEP 2: CLASSIFYING URLs (UNIFIED SOFT → HARD)
[2025-07-29 17:34:28,193: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: --------------------------------------------------
[2025-07-29 17:34:28,193: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting URL classification using proper service methods
[2025-07-29 17:34:28,193: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting soft URL classification
[2025-07-29 17:34:28,193: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][test-analysis][NO_REF] INFO: Starting URL processing for model policy result
{
  "website": "https://devomkids.com/",
  "total_urls": 49
}
[2025-07-29 17:34:28,379: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][test-analysis][NO_REF] INFO: Token calculation: system=265, base_user=988, available_for_urls=87747
[2025-07-29 17:34:28,380: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][test-analysis][NO_REF] INFO: Total URL tokens: 885, Available: 87747
[2025-07-29 17:34:28,380: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][test-analysis][NO_REF] INFO: All URLs fit within token limit
[2025-07-29 17:34:28,380: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][test-analysis][NO_REF] INFO: Final URLs after token limiting
{
  "original_url_count": 49,
  "final_url_count": 49,
  "urls_trimmed": 0,
  "system_tokens": 265,
  "base_user_tokens": 988,
  "url_tokens": 885,
  "final_total_tokens": 2138,
  "token_limit": 90000,
  "remaining_tokens": 87862
}
[2025-07-29 17:34:28,382: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][test-analysis][NO_REF] INFO: Final prompt verification
{
  "actual_prompt_tokens": 2380,
  "token_limit": 90000,
  "within_limit": true,
  "processing_time": 0.18851709365844727
}
[2025-07-29 17:34:28,382: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:28][gemini_optimizer_hard_classification][NO_REF] INFO: Optimizing Gemini call for task_type: hard_classification
{
  "received_task_type": "hard_classification"
}
[2025-07-29 17:34:29,382: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:29][hard_classification_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 9256,
  "context": {
    "task_type": "hard_classification"
  }
}
[2025-07-29 17:34:29,383: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:29][hard_classification_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-29 17:34:29,449: INFO/ForkPoolWorker-7] AFC is enabled with max remote calls: 20000.
[2025-07-29 17:34:29,451: DEBUG/ForkPoolWorker-7] connect_tcp.started host='generativelanguage.googleapis.com' port=443 local_address=None timeout=None socket_options=None
[2025-07-29 17:34:29,566: DEBUG/ForkPoolWorker-7] connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x79b744858d70>
[2025-07-29 17:34:29,566: DEBUG/ForkPoolWorker-7] start_tls.started ssl_context=<ssl.SSLContext object at 0x79b745008050> server_hostname='generativelanguage.googleapis.com' timeout=None
[2025-07-29 17:34:29,614: DEBUG/ForkPoolWorker-7] start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x79b745233750>
[2025-07-29 17:34:29,614: DEBUG/ForkPoolWorker-7] send_request_headers.started request=<Request [b'POST']>
[2025-07-29 17:34:29,614: DEBUG/ForkPoolWorker-7] send_request_headers.complete
[2025-07-29 17:34:29,614: DEBUG/ForkPoolWorker-7] send_request_body.started request=<Request [b'POST']>
[2025-07-29 17:34:29,615: DEBUG/ForkPoolWorker-7] send_request_body.complete
[2025-07-29 17:34:29,615: DEBUG/ForkPoolWorker-7] receive_response_headers.started request=<Request [b'POST']>
[2025-07-29 17:34:57,511: DEBUG/ForkPoolWorker-7] receive_response_headers.complete return_value=(b'HTTP/1.1', 500, b'Internal Server Error', [(b'Vary', b'Origin'), (b'Vary', b'X-Origin'), (b'Vary', b'Referer'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Content-Encoding', b'gzip'), (b'Date', b'Tue, 29 Jul 2025 12:04:57 GMT'), (b'Server', b'scaffolding on HTTPServer2'), (b'X-XSS-Protection', b'0'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Content-Type-Options', b'nosniff'), (b'Server-Timing', b'gfet4t7; dur=27873'), (b'Alt-Svc', b'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'), (b'Transfer-Encoding', b'chunked')])
[2025-07-29 17:34:57,512: INFO/ForkPoolWorker-7] HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 500 Internal Server Error"
[2025-07-29 17:34:57,512: DEBUG/ForkPoolWorker-7] receive_response_body.started request=<Request [b'POST']>
[2025-07-29 17:34:57,512: DEBUG/ForkPoolWorker-7] receive_response_body.complete
[2025-07-29 17:34:57,512: DEBUG/ForkPoolWorker-7] response_closed.started
[2025-07-29 17:34:57,513: DEBUG/ForkPoolWorker-7] response_closed.complete
[2025-07-29 17:34:57,513: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:57][hard_classification_unknown][NO_REF] ERROR: Gemini API error on attempt 1
{
  "error": "{'error': \"500 INTERNAL. {'error': {'code': 500, 'message': 'An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting', 'status': 'INTERNAL'}}\", 'error_type': 'ServerError'}"
}
[2025-07-29 17:34:57,513: WARNING/ForkPoolWorker-7] Traceback for analysis hard_classification_unknown:
[2025-07-29 17:34:57,513: WARNING/ForkPoolWorker-7] Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-29 17:34:57,513: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:57][hard_classification_unknown][NO_REF] INFO: Waiting 2s before retry...
[2025-07-29 17:34:59,513: WARNING/ForkPoolWorker-7] [2025-07-29 17:34:59][hard_classification_unknown][NO_REF] INFO: Gemini API attempt 2/3
[2025-07-29 17:34:59,532: INFO/ForkPoolWorker-7] AFC is enabled with max remote calls: 20000.
[2025-07-29 17:34:59,533: DEBUG/ForkPoolWorker-7] connect_tcp.started host='generativelanguage.googleapis.com' port=443 local_address=None timeout=None socket_options=None
[2025-07-29 17:34:59,554: DEBUG/ForkPoolWorker-7] connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x79b74485f610>
[2025-07-29 17:34:59,554: DEBUG/ForkPoolWorker-7] start_tls.started ssl_context=<ssl.SSLContext object at 0x79b74500b0b0> server_hostname='generativelanguage.googleapis.com' timeout=None
[2025-07-29 17:34:59,603: DEBUG/ForkPoolWorker-7] start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x79b744854c30>
[2025-07-29 17:34:59,603: DEBUG/ForkPoolWorker-7] send_request_headers.started request=<Request [b'POST']>
[2025-07-29 17:34:59,603: DEBUG/ForkPoolWorker-7] send_request_headers.complete
[2025-07-29 17:34:59,603: DEBUG/ForkPoolWorker-7] send_request_body.started request=<Request [b'POST']>
[2025-07-29 17:34:59,604: DEBUG/ForkPoolWorker-7] send_request_body.complete
[2025-07-29 17:34:59,604: DEBUG/ForkPoolWorker-7] receive_response_headers.started request=<Request [b'POST']>
[2025-07-29 17:35:25,923: DEBUG/ForkPoolWorker-7] receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Content-Type', b'application/json; charset=UTF-8'), (b'Vary', b'Origin'), (b'Vary', b'X-Origin'), (b'Vary', b'Referer'), (b'Content-Encoding', b'gzip'), (b'Date', b'Tue, 29 Jul 2025 12:05:25 GMT'), (b'Server', b'scaffolding on HTTPServer2'), (b'X-XSS-Protection', b'0'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Content-Type-Options', b'nosniff'), (b'Server-Timing', b'gfet4t7; dur=26291'), (b'Alt-Svc', b'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'), (b'Transfer-Encoding', b'chunked')])
[2025-07-29 17:35:25,923: INFO/ForkPoolWorker-7] HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
[2025-07-29 17:35:25,923: DEBUG/ForkPoolWorker-7] receive_response_body.started request=<Request [b'POST']>
[2025-07-29 17:35:25,924: DEBUG/ForkPoolWorker-7] receive_response_body.complete
[2025-07-29 17:35:25,924: DEBUG/ForkPoolWorker-7] response_closed.started
[2025-07-29 17:35:25,924: DEBUG/ForkPoolWorker-7] response_closed.complete
[2025-07-29 17:35:25,925: INFO/ForkPoolWorker-7] AFC remote call 1 is done.
[2025-07-29 17:35:25,925: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:25][hard_classification_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=189 candidates_tokens_details=None prompt_token_count=2739 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=2739
)] thoughts_token_count=5255 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=8183 traffic_type=None
[2025-07-29 17:35:25,925: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:25][hard_classification_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 2,
  "response_length": 493,
  "finish_reason": "STOP"
}
[2025-07-29 17:35:27,938: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Soft classification completed for 49 URLs
[2025-07-29 17:35:27,938: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ RESULT: URL classification completed
[2025-07-29 17:35:27,938: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📊 Categories found: 15
[2025-07-29 17:35:27,938: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📋 Categories: home_page, about_us, privacy_policy, terms_and_condition, returns_cancellation_exchange, shipping_delivery, contact_us, instagram, facebook, twitter, linkedin, youtube, pinterest, x, other
[2025-07-29 17:35:27,938: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 
🔀 STEP 3: DETERMINING PROCESSING FLOW
--------------------------------------------------
[2025-07-29 17:35:27,938: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ NORMAL FLOW SELECTED: All URLs are reachable
{
  "backup_needed": false,
  "trigger_reason": "UNREACHABLE_VIA_TOOL_IS_EMPTY",
  "unreachable_via_tool_count": 0,
  "flow_decision": "NORMAL_FLOW",
  "note": "Will use hard classification for policy + soft classification for social media"
}
[2025-07-29 17:35:27,938: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Reachability calculation completed
{
  "total_priority_urls": 10,
  "priority_urls_reachable": 10,
  "priority_urls_not_reachable": 0,
  "reachability_percentage": 100.0
}
[2025-07-29 17:35:27,938: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ DECISION: Using NORMAL FLOW (HARD CLASSIFICATION)
{
  "reason": "All URLs verified as reachable",
  "method": "Hard classification for policy, soft for social media",
  "reachability": "100.00%"
}
[2025-07-29 17:35:27,938: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ Using hard classification results (social media already merged by URL classification service)
[2025-07-29 17:35:27,938: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Processing hard classification flow
[2025-07-29 17:35:27,938: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🎯 Starting representative URL selection with priority order implementation
{
  "total_categories": 15,
  "categories_with_urls": 5
}
[2025-07-29 17:35:27,938: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ Priority selection: terms_and_condition -> terms_and_condition
{
  "selected_url": "https://devomkids.com/policies/terms-of-service",
  "total_urls_in_category": 2,
  "urls_skipped": 1,
  "priority_rank": 1
}
[2025-07-29 17:35:27,939: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ Priority selection: privacy_policy -> privacy_policy
{
  "selected_url": "https://devomkids.com/policies/privacy-policy",
  "total_urls_in_category": 5,
  "urls_skipped": 4,
  "priority_rank": 2
}
[2025-07-29 17:35:27,939: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ Priority selection: contact_us -> contact_us
{
  "selected_url": "https://devomkids.com/pages/contact",
  "total_urls_in_category": 2,
  "urls_skipped": 1,
  "priority_rank": 4
}
[2025-07-29 17:35:27,939: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ Priority selection: about_us -> about_us
{
  "selected_url": "https://devomkids.com/pages/about-us",
  "total_urls_in_category": 1,
  "urls_skipped": 0,
  "priority_rank": 6
}
[2025-07-29 17:35:27,939: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🏁 Representative URL selection completed
{
  "selected_categories": 4,
  "categories": [
    "terms_and_condition",
    "privacy_policy",
    "contact_us",
    "about_us"
  ],
  "total_urls_selected": 4
}
[2025-07-29 17:35:27,939: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Representative URLs selected
{
  "categories_selected": 4,
  "categories": [
    "terms_and_condition",
    "privacy_policy",
    "contact_us",
    "about_us"
  ]
}
[2025-07-29 17:35:27,939: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🔄 Starting content extraction for all categories
{
  "total_categories": 4,
  "categories": [
    "terms_and_condition",
    "privacy_policy",
    "contact_us",
    "about_us"
  ]
}
[2025-07-29 17:35:27,939: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Extracting content for terms_and_condition
{
  "url": "https://devomkids.com/policies/terms-of-service"
}
[2025-07-29 17:35:27,939: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting content extraction for terms_and_condition
{
  "url": "https://devomkids.com/policies/terms-of-service"
}
[2025-07-29 17:35:27,939: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://devomkids.com/policies/terms-of-service"
}
[2025-07-29 17:35:27,939: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:27][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://devomkids.com/policies/terms-of-service",
  "method": "requests",
  "attempt": 1
}
[2025-07-29 17:35:27,940: DEBUG/ForkPoolWorker-7] Starting new HTTPS connection (1): devomkids.com:443
[2025-07-29 17:35:28,803: DEBUG/ForkPoolWorker-7] https://devomkids.com:443 "GET /policies/terms-of-service HTTP/1.1" 200 None
[2025-07-29 17:35:28,813: DEBUG/ForkPoolWorker-7] close.started
[2025-07-29 17:35:28,814: DEBUG/ForkPoolWorker-7] close.complete
[2025-07-29 17:35:28,814: DEBUG/ForkPoolWorker-7] close.started
[2025-07-29 17:35:28,814: DEBUG/ForkPoolWorker-7] close.complete
[2025-07-29 17:35:28,848: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:28][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Text extraction successful with requests
{
  "url": "https://devomkids.com/policies/terms-of-service",
  "text_length": 19442,
  "method": "requests"
}
[2025-07-29 17:35:28,848: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:28][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://devomkids.com/policies/terms-of-service: close_popups=False (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-29 17:35:28,848: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:28][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://devomkids.com/policies/terms-of-service: 3000ms
[2025-07-29 17:35:29,217: INFO/ForkPoolWorker-7] Starting OPTIMIZED screenshot capture for https://devomkids.com/policies/terms-of-service
[2025-07-29 17:35:29,806: INFO/ForkPoolWorker-7] Navigating to https://devomkids.com/policies/terms-of-service
[2025-07-29 17:35:35,396: INFO/ForkPoolWorker-7] Waiting 3000ms for images to load...
[2025-07-29 17:35:38,401: INFO/ForkPoolWorker-7] Taking optimized screenshot
[2025-07-29 17:35:39,493: INFO/ForkPoolWorker-7] Screenshot captured in 10.28s, size: 1153928 bytes
[2025-07-29 17:35:39,544: DEBUG/ForkPoolWorker-7] Browser closed successfully
[2025-07-29 17:35:39,551: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:39][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753790739_3d2ff795-892d-4660-bc55-ce9a169a6c0e.png"
}
[2025-07-29 17:35:39,552: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:39][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] ERROR: Unexpected error during Azure upload
{
  "error": "{'error': 'Connection string missing required connection details.', 'filename': '1753790739_3d2ff795-892d-4660-bc55-ce9a169a6c0e.png'}"
}
[2025-07-29 17:35:39,552: WARNING/ForkPoolWorker-7] Traceback for analysis cffbff75-39a0-455a-97e2-609dcef74a90:
[2025-07-29 17:35:39,552: WARNING/ForkPoolWorker-7] Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-29 17:35:39,552: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:39][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] ERROR: Error in _capture_and_upload_screenshot for https://devomkids.com/policies/terms-of-service
{
  "error": "Connection string missing required connection details."
}
[2025-07-29 17:35:39,552: WARNING/ForkPoolWorker-7] Traceback for analysis cffbff75-39a0-455a-97e2-609dcef74a90:
[2025-07-29 17:35:39,554: WARNING/ForkPoolWorker-7] Traceback (most recent call last):
[2025-07-29 17:35:39,554: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/azure/storage/blob/_shared/base_client_async.py", line 248, in parse_connection_str
    f"https://{conn_settings['ACCOUNTNAME']}."
               ~~~~~~~~~~~~~^^^^^^^^^^^^^^^
[2025-07-29 17:35:39,554: WARNING/ForkPoolWorker-7] KeyError: 'ACCOUNTNAME'
[2025-07-29 17:35:39,554: WARNING/ForkPoolWorker-7] 
The above exception was the direct cause of the following exception:
[2025-07-29 17:35:39,554: WARNING/ForkPoolWorker-7] Traceback (most recent call last):
[2025-07-29 17:35:39,555: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/app/services/policy_analysis_enhanced_service.py", line 109, in _capture_and_upload_screenshot
    azure_url = await upload_to_azure_container(temp_file_path, filename, self.logger)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-29 17:35:39,555: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/app/services/screenshot/blob_utils.py", line 42, in upload_to_azure_container
    blob_service_client = BlobServiceClient.from_connection_string(AZURE_CONNECTION_STRING)
[2025-07-29 17:35:39,555: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/azure/storage/blob/aio/_blob_service_client_async.py", line 192, in from_connection_string
    account_url, secondary, credential = parse_connection_str(conn_str, credential, 'blob')
                                         ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-29 17:35:39,555: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/azure/storage/blob/_shared/base_client_async.py", line 252, in parse_connection_str
    raise ValueError("Connection string missing required connection details.") from exc
[2025-07-29 17:35:39,555: WARNING/ForkPoolWorker-7] ValueError: Connection string missing required connection details.
[2025-07-29 17:35:39,555: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:39][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Content extraction completed for terms_and_condition
{
  "url": "https://devomkids.com/policies/terms-of-service",
  "text_length": 19442,
  "screenshot_status": "failed"
}
[2025-07-29 17:35:39,555: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:39][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Content extraction completed for terms_and_condition
{
  "url": "https://devomkids.com/policies/terms-of-service",
  "text_length": 19442,
  "has_screenshot": false
}
[2025-07-29 17:35:39,555: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:39][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Extracting content for privacy_policy
{
  "url": "https://devomkids.com/policies/privacy-policy"
}
[2025-07-29 17:35:39,555: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:39][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting content extraction for privacy_policy
{
  "url": "https://devomkids.com/policies/privacy-policy"
}
[2025-07-29 17:35:39,555: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:39][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://devomkids.com/policies/privacy-policy"
}
[2025-07-29 17:35:39,555: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:39][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://devomkids.com/policies/privacy-policy",
  "method": "requests",
  "attempt": 1
}
[2025-07-29 17:35:39,556: DEBUG/ForkPoolWorker-7] Starting new HTTPS connection (1): devomkids.com:443
[2025-07-29 17:35:39,857: DEBUG/ForkPoolWorker-7] https://devomkids.com:443 "GET /policies/privacy-policy HTTP/1.1" 200 None
[2025-07-29 17:35:39,894: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:39][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Text extraction successful with requests
{
  "url": "https://devomkids.com/policies/privacy-policy",
  "text_length": 17113,
  "method": "requests"
}
[2025-07-29 17:35:39,894: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:39][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://devomkids.com/policies/privacy-policy: close_popups=False (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-29 17:35:39,894: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:39][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://devomkids.com/policies/privacy-policy: 3000ms
[2025-07-29 17:35:40,125: INFO/ForkPoolWorker-7] Starting OPTIMIZED screenshot capture for https://devomkids.com/policies/privacy-policy
[2025-07-29 17:35:40,620: INFO/ForkPoolWorker-7] Navigating to https://devomkids.com/policies/privacy-policy
[2025-07-29 17:35:41,349: INFO/ForkPoolWorker-7] Waiting 3000ms for images to load...
[2025-07-29 17:35:44,359: INFO/ForkPoolWorker-7] Taking optimized screenshot
[2025-07-29 17:35:45,457: INFO/ForkPoolWorker-7] Screenshot captured in 5.33s, size: 1102440 bytes
[2025-07-29 17:35:45,504: DEBUG/ForkPoolWorker-7] Browser closed successfully
[2025-07-29 17:35:45,513: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:45][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753790745_a0084355-7004-4c9d-836c-2e685ecbaa64.png"
}
[2025-07-29 17:35:45,513: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:45][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] ERROR: Unexpected error during Azure upload
{
  "error": "{'error': 'Connection string missing required connection details.', 'filename': '1753790745_a0084355-7004-4c9d-836c-2e685ecbaa64.png'}"
}
[2025-07-29 17:35:45,513: WARNING/ForkPoolWorker-7] Traceback for analysis cffbff75-39a0-455a-97e2-609dcef74a90:
[2025-07-29 17:35:45,513: WARNING/ForkPoolWorker-7] Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-29 17:35:45,513: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:45][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] ERROR: Error in _capture_and_upload_screenshot for https://devomkids.com/policies/privacy-policy
{
  "error": "Connection string missing required connection details."
}
[2025-07-29 17:35:45,513: WARNING/ForkPoolWorker-7] Traceback for analysis cffbff75-39a0-455a-97e2-609dcef74a90:
[2025-07-29 17:35:45,513: WARNING/ForkPoolWorker-7] Traceback (most recent call last):
[2025-07-29 17:35:45,514: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/azure/storage/blob/_shared/base_client_async.py", line 248, in parse_connection_str
    f"https://{conn_settings['ACCOUNTNAME']}."
               ~~~~~~~~~~~~~^^^^^^^^^^^^^^^
[2025-07-29 17:35:45,514: WARNING/ForkPoolWorker-7] KeyError: 'ACCOUNTNAME'
[2025-07-29 17:35:45,514: WARNING/ForkPoolWorker-7] 
The above exception was the direct cause of the following exception:
[2025-07-29 17:35:45,514: WARNING/ForkPoolWorker-7] Traceback (most recent call last):
[2025-07-29 17:35:45,514: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/app/services/policy_analysis_enhanced_service.py", line 109, in _capture_and_upload_screenshot
    azure_url = await upload_to_azure_container(temp_file_path, filename, self.logger)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-29 17:35:45,514: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/app/services/screenshot/blob_utils.py", line 42, in upload_to_azure_container
    blob_service_client = BlobServiceClient.from_connection_string(AZURE_CONNECTION_STRING)
[2025-07-29 17:35:45,514: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/azure/storage/blob/aio/_blob_service_client_async.py", line 192, in from_connection_string
    account_url, secondary, credential = parse_connection_str(conn_str, credential, 'blob')
                                         ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-29 17:35:45,514: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/azure/storage/blob/_shared/base_client_async.py", line 252, in parse_connection_str
    raise ValueError("Connection string missing required connection details.") from exc
[2025-07-29 17:35:45,514: WARNING/ForkPoolWorker-7] ValueError: Connection string missing required connection details.
[2025-07-29 17:35:45,515: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:45][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Content extraction completed for privacy_policy
{
  "url": "https://devomkids.com/policies/privacy-policy",
  "text_length": 17113,
  "screenshot_status": "failed"
}
[2025-07-29 17:35:45,515: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:45][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Content extraction completed for privacy_policy
{
  "url": "https://devomkids.com/policies/privacy-policy",
  "text_length": 17113,
  "has_screenshot": false
}
[2025-07-29 17:35:45,515: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:45][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Extracting content for contact_us
{
  "url": "https://devomkids.com/pages/contact"
}
[2025-07-29 17:35:45,515: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:45][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting content extraction for contact_us
{
  "url": "https://devomkids.com/pages/contact"
}
[2025-07-29 17:35:45,515: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:45][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://devomkids.com/pages/contact"
}
[2025-07-29 17:35:45,515: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:45][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://devomkids.com/pages/contact",
  "method": "requests",
  "attempt": 1
}
[2025-07-29 17:35:45,516: DEBUG/ForkPoolWorker-7] Starting new HTTPS connection (1): devomkids.com:443
[2025-07-29 17:35:45,837: DEBUG/ForkPoolWorker-7] https://devomkids.com:443 "GET /pages/contact HTTP/1.1" 200 None
[2025-07-29 17:35:45,871: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:45][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Text extraction successful with requests
{
  "url": "https://devomkids.com/pages/contact",
  "text_length": 1207,
  "method": "requests"
}
[2025-07-29 17:35:45,871: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:45][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://devomkids.com/pages/contact: close_popups=False (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-29 17:35:45,871: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:45][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://devomkids.com/pages/contact: 3000ms
[2025-07-29 17:35:46,098: INFO/ForkPoolWorker-7] Starting OPTIMIZED screenshot capture for https://devomkids.com/pages/contact
[2025-07-29 17:35:46,566: INFO/ForkPoolWorker-7] Navigating to https://devomkids.com/pages/contact
[2025-07-29 17:35:47,522: INFO/ForkPoolWorker-7] Waiting 3000ms for images to load...
[2025-07-29 17:35:50,529: INFO/ForkPoolWorker-7] Taking optimized screenshot
[2025-07-29 17:35:50,773: INFO/ForkPoolWorker-7] Screenshot captured in 4.67s, size: 69723 bytes
[2025-07-29 17:35:50,824: DEBUG/ForkPoolWorker-7] Browser closed successfully
[2025-07-29 17:35:50,832: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:50][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753790750_3d0c37dc-1c88-41a3-89c8-4d9d9717f553.png"
}
[2025-07-29 17:35:50,832: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:50][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] ERROR: Unexpected error during Azure upload
{
  "error": "{'error': 'Connection string missing required connection details.', 'filename': '1753790750_3d0c37dc-1c88-41a3-89c8-4d9d9717f553.png'}"
}
[2025-07-29 17:35:50,833: WARNING/ForkPoolWorker-7] Traceback for analysis cffbff75-39a0-455a-97e2-609dcef74a90:
[2025-07-29 17:35:50,833: WARNING/ForkPoolWorker-7] Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-29 17:35:50,833: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:50][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] ERROR: Error in _capture_and_upload_screenshot for https://devomkids.com/pages/contact
{
  "error": "Connection string missing required connection details."
}
[2025-07-29 17:35:50,833: WARNING/ForkPoolWorker-7] Traceback for analysis cffbff75-39a0-455a-97e2-609dcef74a90:
[2025-07-29 17:35:50,833: WARNING/ForkPoolWorker-7] Traceback (most recent call last):
[2025-07-29 17:35:50,833: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/azure/storage/blob/_shared/base_client_async.py", line 248, in parse_connection_str
    f"https://{conn_settings['ACCOUNTNAME']}."
               ~~~~~~~~~~~~~^^^^^^^^^^^^^^^
[2025-07-29 17:35:50,833: WARNING/ForkPoolWorker-7] KeyError: 'ACCOUNTNAME'
[2025-07-29 17:35:50,834: WARNING/ForkPoolWorker-7] 
The above exception was the direct cause of the following exception:
[2025-07-29 17:35:50,834: WARNING/ForkPoolWorker-7] Traceback (most recent call last):
[2025-07-29 17:35:50,834: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/app/services/policy_analysis_enhanced_service.py", line 109, in _capture_and_upload_screenshot
    azure_url = await upload_to_azure_container(temp_file_path, filename, self.logger)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-29 17:35:50,834: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/app/services/screenshot/blob_utils.py", line 42, in upload_to_azure_container
    blob_service_client = BlobServiceClient.from_connection_string(AZURE_CONNECTION_STRING)
[2025-07-29 17:35:50,834: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/azure/storage/blob/aio/_blob_service_client_async.py", line 192, in from_connection_string
    account_url, secondary, credential = parse_connection_str(conn_str, credential, 'blob')
                                         ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-29 17:35:50,834: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/azure/storage/blob/_shared/base_client_async.py", line 252, in parse_connection_str
    raise ValueError("Connection string missing required connection details.") from exc
[2025-07-29 17:35:50,834: WARNING/ForkPoolWorker-7] ValueError: Connection string missing required connection details.
[2025-07-29 17:35:50,835: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:50][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Content extraction completed for contact_us
{
  "url": "https://devomkids.com/pages/contact",
  "text_length": 1207,
  "screenshot_status": "failed"
}
[2025-07-29 17:35:50,835: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:50][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Content extraction completed for contact_us
{
  "url": "https://devomkids.com/pages/contact",
  "text_length": 1207,
  "has_screenshot": false
}
[2025-07-29 17:35:50,835: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:50][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Extracting content for about_us
{
  "url": "https://devomkids.com/pages/about-us"
}
[2025-07-29 17:35:50,835: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:50][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting content extraction for about_us
{
  "url": "https://devomkids.com/pages/about-us"
}
[2025-07-29 17:35:50,835: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:50][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://devomkids.com/pages/about-us"
}
[2025-07-29 17:35:50,835: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:50][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://devomkids.com/pages/about-us",
  "method": "requests",
  "attempt": 1
}
[2025-07-29 17:35:50,836: DEBUG/ForkPoolWorker-7] Starting new HTTPS connection (1): devomkids.com:443
[2025-07-29 17:35:51,206: DEBUG/ForkPoolWorker-7] https://devomkids.com:443 "GET /pages/about-us HTTP/1.1" 200 None
[2025-07-29 17:35:51,244: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:51][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Text extraction successful with requests
{
  "url": "https://devomkids.com/pages/about-us",
  "text_length": 2340,
  "method": "requests"
}
[2025-07-29 17:35:51,244: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:51][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://devomkids.com/pages/about-us: close_popups=False (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-29 17:35:51,244: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:51][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://devomkids.com/pages/about-us: 3000ms
[2025-07-29 17:35:51,470: INFO/ForkPoolWorker-7] Starting OPTIMIZED screenshot capture for https://devomkids.com/pages/about-us
[2025-07-29 17:35:51,967: INFO/ForkPoolWorker-7] Navigating to https://devomkids.com/pages/about-us
[2025-07-29 17:35:52,575: INFO/ForkPoolWorker-7] Waiting 3000ms for images to load...
[2025-07-29 17:35:55,580: INFO/ForkPoolWorker-7] Taking optimized screenshot
[2025-07-29 17:35:55,875: INFO/ForkPoolWorker-7] Screenshot captured in 4.40s, size: 299348 bytes
[2025-07-29 17:35:55,916: DEBUG/ForkPoolWorker-7] Browser closed successfully
[2025-07-29 17:35:55,923: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753790755_a05df570-e301-42cd-aabb-c41f716e8e43.png"
}
[2025-07-29 17:35:55,923: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] ERROR: Unexpected error during Azure upload
{
  "error": "{'error': 'Connection string missing required connection details.', 'filename': '1753790755_a05df570-e301-42cd-aabb-c41f716e8e43.png'}"
}
[2025-07-29 17:35:55,923: WARNING/ForkPoolWorker-7] Traceback for analysis cffbff75-39a0-455a-97e2-609dcef74a90:
[2025-07-29 17:35:55,923: WARNING/ForkPoolWorker-7] Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-29 17:35:55,923: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] ERROR: Error in _capture_and_upload_screenshot for https://devomkids.com/pages/about-us
{
  "error": "Connection string missing required connection details."
}
[2025-07-29 17:35:55,923: WARNING/ForkPoolWorker-7] Traceback for analysis cffbff75-39a0-455a-97e2-609dcef74a90:
[2025-07-29 17:35:55,923: WARNING/ForkPoolWorker-7] Traceback (most recent call last):
[2025-07-29 17:35:55,923: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/azure/storage/blob/_shared/base_client_async.py", line 248, in parse_connection_str
    f"https://{conn_settings['ACCOUNTNAME']}."
               ~~~~~~~~~~~~~^^^^^^^^^^^^^^^
[2025-07-29 17:35:55,923: WARNING/ForkPoolWorker-7] KeyError: 'ACCOUNTNAME'
[2025-07-29 17:35:55,924: WARNING/ForkPoolWorker-7] 
The above exception was the direct cause of the following exception:
[2025-07-29 17:35:55,924: WARNING/ForkPoolWorker-7] Traceback (most recent call last):
[2025-07-29 17:35:55,924: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/app/services/policy_analysis_enhanced_service.py", line 109, in _capture_and_upload_screenshot
    azure_url = await upload_to_azure_container(temp_file_path, filename, self.logger)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-29 17:35:55,924: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/app/services/screenshot/blob_utils.py", line 42, in upload_to_azure_container
    blob_service_client = BlobServiceClient.from_connection_string(AZURE_CONNECTION_STRING)
[2025-07-29 17:35:55,924: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/azure/storage/blob/aio/_blob_service_client_async.py", line 192, in from_connection_string
    account_url, secondary, credential = parse_connection_str(conn_str, credential, 'blob')
                                         ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-29 17:35:55,924: WARNING/ForkPoolWorker-7]   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/azure/storage/blob/_shared/base_client_async.py", line 252, in parse_connection_str
    raise ValueError("Connection string missing required connection details.") from exc
[2025-07-29 17:35:55,924: WARNING/ForkPoolWorker-7] ValueError: Connection string missing required connection details.
[2025-07-29 17:35:55,924: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Content extraction completed for about_us
{
  "url": "https://devomkids.com/pages/about-us",
  "text_length": 2340,
  "screenshot_status": "failed"
}
[2025-07-29 17:35:55,924: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Content extraction completed for about_us
{
  "url": "https://devomkids.com/pages/about-us",
  "text_length": 2340,
  "has_screenshot": false
}
[2025-07-29 17:35:55,924: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ Content extraction completed for all categories
{
  "categories_processed": 13,
  "categories_with_content": 4
}
[2025-07-29 17:35:55,924: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Hard classification flow completed
{
  "categories_processed": 13
}
[2025-07-29 17:35:55,924: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 
[2025-07-29 17:35:55,925: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 💾 STEP 4: SAVING ANALYSIS RESULTS
[2025-07-29 17:35:55,925: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ----------------------------------------
2025-07-29 17:35:55,925 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-29 17:35:55,925: INFO/ForkPoolWorker-7] BEGIN (implicit)
2025-07-29 17:35:55,925 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = ?
[2025-07-29 17:35:55,925: INFO/ForkPoolWorker-7] SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = ?
2025-07-29 17:35:55,925 INFO sqlalchemy.engine.Engine [cached since 87.74s ago] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
[2025-07-29 17:35:55,925: INFO/ForkPoolWorker-7] [cached since 87.74s ago] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
2025-07-29 17:35:55,928 INFO sqlalchemy.engine.Engine UPDATE policy_analysis_new_gemini SET reachability_percentage=?, total_urls_processed=?, returns_cancellation_exchange_url=?, returns_cancellation_exchange_text=?, returns_cancellation_exchange_screenshot=?, privacy_policy_url=?, privacy_policy_text=?, privacy_policy_screenshot=?, terms_and_condition_url=?, terms_and_condition_text=?, terms_and_condition_screenshot=?, shipping_delivery_url=?, shipping_delivery_text=?, shipping_delivery_screenshot=?, contact_us_url=?, contact_us_text=?, contact_us_screenshot=?, about_us_url=?, about_us_text=?, about_us_screenshot=?, instagram_url=?, instagram_text=?, instagram_screenshot=?, youtube_url=?, youtube_text=?, youtube_screenshot=?, facebook_url=?, facebook_text=?, facebook_screenshot=?, twitter_url=?, twitter_text=?, twitter_screenshot=?, linkedin_url=?, linkedin_text=?, linkedin_screenshot=?, pinterest_url=?, pinterest_text=?, pinterest_screenshot=?, x_url=?, x_text=?, x_screenshot=?, completed_at=?, processing_status=? WHERE policy_analysis_new_gemini.id = ?
[2025-07-29 17:35:55,928: INFO/ForkPoolWorker-7] UPDATE policy_analysis_new_gemini SET reachability_percentage=?, total_urls_processed=?, returns_cancellation_exchange_url=?, returns_cancellation_exchange_text=?, returns_cancellation_exchange_screenshot=?, privacy_policy_url=?, privacy_policy_text=?, privacy_policy_screenshot=?, terms_and_condition_url=?, terms_and_condition_text=?, terms_and_condition_screenshot=?, shipping_delivery_url=?, shipping_delivery_text=?, shipping_delivery_screenshot=?, contact_us_url=?, contact_us_text=?, contact_us_screenshot=?, about_us_url=?, about_us_text=?, about_us_screenshot=?, instagram_url=?, instagram_text=?, instagram_screenshot=?, youtube_url=?, youtube_text=?, youtube_screenshot=?, facebook_url=?, facebook_text=?, facebook_screenshot=?, twitter_url=?, twitter_text=?, twitter_screenshot=?, linkedin_url=?, linkedin_text=?, linkedin_screenshot=?, pinterest_url=?, pinterest_text=?, pinterest_screenshot=?, x_url=?, x_text=?, x_screenshot=?, completed_at=?, processing_status=? WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:35:55,928 INFO sqlalchemy.engine.Engine [generated in 0.00033s] (100.0, 13, 'not_found', 'not_applicable', 'not_applicable', 'https://devomkids.com/policies/privacy-policy', 'Privacy policy – Devom Kids Skip to content Welcome to our store India\'s First Religious Store for Kids Get 10% off on orders above 999/- Home About ... (16824 characters truncated) ... Shopify Refund policy Privacy policy Terms of service Contact information Choosing a selection results in a full page refresh. Opens in a new window.', 'screenshot_failed', 'https://devomkids.com/policies/terms-of-service', "Terms of service – Devom Kids Skip to content Welcome to our store India's First Religious Store for Kids Get 10% off on orders above 999/- Home Abou ... (19147 characters truncated) ... Shopify Refund policy Privacy policy Terms of service Contact information Choosing a selection results in a full page refresh. Opens in a new window.", 'screenshot_failed', 'not_found', 'not_applicable', 'not_applicable', 'https://devomkids.com/pages/contact', "Contact Us – Devom Kids Skip to content Welcome to our store India's First Religious Store for Kids Get 10% off on orders above 999/- Home About Us C ... (909 characters truncated) ... Shopify Refund policy Privacy policy Terms of service Contact information Choosing a selection results in a full page refresh. Opens in a new window.", 'screenshot_failed', 'https://devomkids.com/pages/about-us', "Devom Kids: About us Skip to content Welcome to our store India's First Religious Store for Kids Get 10% off on orders above 999/- Home About Us Coll ... (2048 characters truncated) ... Shopify Refund policy Privacy policy Terms of service Contact information Choosing a selection results in a full page refresh. Opens in a new window.", 'screenshot_failed', 'not_found', 'not_applicable', 'not_applicable', 'not_found', 'not_applicable', 'not_applicable', 'not_found', 'not_applicable', 'not_applicable', 'not_found', 'not_applicable', 'not_applicable', 'not_found', 'not_applicable', 'not_applicable', 'not_found', 'not_applicable', 'not_applicable', 'not_found', 'not_applicable', 'not_applicable', '2025-07-29T17:35:55.926518Z', 'COMPLETED', 1)
[2025-07-29 17:35:55,928: INFO/ForkPoolWorker-7] [generated in 0.00033s] (100.0, 13, 'not_found', 'not_applicable', 'not_applicable', 'https://devomkids.com/policies/privacy-policy', 'Privacy policy – Devom Kids Skip to content Welcome to our store India\'s First Religious Store for Kids Get 10% off on orders above 999/- Home About ... (16824 characters truncated) ... Shopify Refund policy Privacy policy Terms of service Contact information Choosing a selection results in a full page refresh. Opens in a new window.', 'screenshot_failed', 'https://devomkids.com/policies/terms-of-service', "Terms of service – Devom Kids Skip to content Welcome to our store India's First Religious Store for Kids Get 10% off on orders above 999/- Home Abou ... (19147 characters truncated) ... Shopify Refund policy Privacy policy Terms of service Contact information Choosing a selection results in a full page refresh. Opens in a new window.", 'screenshot_failed', 'not_found', 'not_applicable', 'not_applicable', 'https://devomkids.com/pages/contact', "Contact Us – Devom Kids Skip to content Welcome to our store India's First Religious Store for Kids Get 10% off on orders above 999/- Home About Us C ... (909 characters truncated) ... Shopify Refund policy Privacy policy Terms of service Contact information Choosing a selection results in a full page refresh. Opens in a new window.", 'screenshot_failed', 'https://devomkids.com/pages/about-us', "Devom Kids: About us Skip to content Welcome to our store India's First Religious Store for Kids Get 10% off on orders above 999/- Home About Us Coll ... (2048 characters truncated) ... Shopify Refund policy Privacy policy Terms of service Contact information Choosing a selection results in a full page refresh. Opens in a new window.", 'screenshot_failed', 'not_found', 'not_applicable', 'not_applicable', 'not_found', 'not_applicable', 'not_applicable', 'not_found', 'not_applicable', 'not_applicable', 'not_found', 'not_applicable', 'not_applicable', 'not_found', 'not_applicable', 'not_applicable', 'not_found', 'not_applicable', 'not_applicable', 'not_found', 'not_applicable', 'not_applicable', '2025-07-29T17:35:55.926518Z', 'COMPLETED', 1)
2025-07-29 17:35:55,929 INFO sqlalchemy.engine.Engine COMMIT
[2025-07-29 17:35:55,929: INFO/ForkPoolWorker-7] COMMIT
[2025-07-29 17:35:55,940: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Analysis results saved to database
{
  "categories_saved": 13,
  "analysis_flow": "normal",
  "reachability_percentage": 100.0
}
[2025-07-29 17:35:55,940: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ RESULT: Analysis results saved successfully
[2025-07-29 17:35:55,940: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 
[2025-07-29 17:35:55,940: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 📤 STEP 5: SENDING WEBHOOK NOTIFICATION
[2025-07-29 17:35:55,940: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ----------------------------------------
[2025-07-29 17:35:55,940: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Starting webhook preparation
{
  "website": "https://devomkids.com/",
  "content_data_keys": [
    "terms_and_condition",
    "privacy_policy",
    "contact_us",
    "about_us",
    "x",
    "shipping_delivery",
    "facebook",
    "instagram",
    "twitter",
    "youtube",
    "linkedin",
    "pinterest",
    "returns_cancellation_exchange"
  ],
  "content_data_size": 13,
  "content_data_details": {
    "terms_and_condition": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "privacy_policy": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "contact_us": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "about_us": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "x": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "shipping_delivery": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "facebook": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "instagram": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "twitter": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "youtube": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "linkedin": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "pinterest": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "returns_cancellation_exchange": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    }
  }
}
[2025-07-29 17:35:55,940: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🚫 Excluding About Us (AU) category from webhook payload
{
  "category": "about_us",
  "reason": "excluded_by_requirement"
}
[2025-07-29 17:35:55,940: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 📱 Collected X data for merging
{
  "category": "x",
  "has_url": true,
  "has_text": true,
  "has_screenshot": true
}
[2025-07-29 17:35:55,940: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 📱 Collected Twitter data for merging
{
  "category": "twitter",
  "has_url": true,
  "has_text": true,
  "has_screenshot": true
}
[2025-07-29 17:35:55,940: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🔄 Merging Twitter and X data - prioritizing X data
{
  "twitter_url": "not_found",
  "x_url": "not_found",
  "selected_source": "X"
}
[2025-07-29 17:35:55,940: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ Successfully merged Twitter/X data
{
  "merge_source": "X (prioritized over Twitter)",
  "final_category": "X",
  "url": "not_found"
}
[2025-07-29 17:35:55,941: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Text truncated from 3182 to 100 words for API compatibility
[2025-07-29 17:35:55,941: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Text truncated from 2709 to 100 words for API compatibility
[2025-07-29 17:35:55,941: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Text truncated from 185 to 100 words for API compatibility
[2025-07-29 17:35:55,941: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 📤 Final webhook payload policy types
{
  "policy_types": [
    "TNC",
    "PP",
    "CU",
    "SD",
    "FB",
    "IG",
    "YT",
    "LI",
    "PT",
    "RAC",
    "X"
  ],
  "total_policies": 11,
  "excluded_categories": [
    "AU"
  ],
  "merged_categories": "TW+X -> X"
}
[2025-07-29 17:35:55,941: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 📤 WEBHOOK REQUEST PREPARATION - Enhanced Policy Service
{
  "webhook_url": "https://bffapi.biztel.ai/api/policy/results",
  "method": "PATCH",
  "headers": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "api_key_configured": true,
  "api_key_length": 8,
  "content_type": "application/json",
  "payload_size_bytes": 3207,
  "payload_size_kb": 3.13,
  "policies_count": 11,
  "scrape_request_ref_id": "cffbff75-39a0-455a-97e2-609dcef74a90",
  "website": "https://devomkids.com/",
  "org_id": 1,
  "timestamp": "2025-07-29T17:35:55.941655"
}
[2025-07-29 17:35:55,941: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 📋 WEBHOOK PAYLOAD STRUCTURE - Enhanced Policy Service
{
  "payload_structure": {
    "website": "https://devomkids.com/",
    "scrapeRequestUuid": "cffbff75-39a0-455a-97e2-609dcef74a90",
    "createdDate": "2025-07-29T17:35:55.941606Z",
    "status": "COMPLETED",
    "org_id": 1,
    "policies_count": 11,
    "policy_types": [
      "TNC",
      "PP",
      "CU",
      "SD",
      "FB",
      "IG",
      "YT",
      "LI",
      "PT",
      "RAC",
      "X"
    ],
    "policy_details": [
      {
        "type": "TNC",
        "has_url": true,
        "has_screenshot": false,
        "has_text": true,
        "url_preview": "https://devomkids.com/policies/terms-of-service",
        "text_length": 608
      },
      {
        "type": "PP",
        "has_url": true,
        "has_screenshot": false,
        "has_text": true,
        "url_preview": "https://devomkids.com/policies/privacy-policy",
        "text_length": 630
      },
      {
        "type": "CU",
        "has_url": true,
        "has_screenshot": false,
        "has_text": true,
        "url_preview": "https://devomkids.com/pages/contact",
        "text_length": 651
      },
      {
        "type": "SD",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "FB",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "IG",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "YT",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "LI",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "PT",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "RAC",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "X",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      }
    ]
  },
  "full_payload": {
    "website": "https://devomkids.com/",
    "scrapeRequestUuid": "cffbff75-39a0-455a-97e2-609dcef74a90",
    "createdDate": "2025-07-29T17:35:55.941606Z",
    "status": "COMPLETED",
    "policies": [
      {
        "type": "TNC",
        "url": "https://devomkids.com/policies/terms-of-service",
        "imglink": "screenshot_failed",
        "text": "Terms of service \u2013 Devom Kids Skip to content Welcome to our store India's First Religious Store for Kids Get 10% off on orders above 999/- Home About Us Collections Products Poster Printables Blogs Contact Log in Instagram Home About Us Collections Products Poster Printables Blogs Contact Search Log in Cart Item added to your cart View cart Check out Continue shopping Terms of service OVERVIEW This website is operated by Devom Kids. Throughout the site, the terms \u201cwe\u201d, \u201cus\u201d and \u201cour\u201d refer to Devom Kids. Devom Kids offers this website, including all information, tools and Services available from this"
      },
      {
        "type": "PP",
        "url": "https://devomkids.com/policies/privacy-policy",
        "imglink": "screenshot_failed",
        "text": "Privacy policy \u2013 Devom Kids Skip to content Welcome to our store India's First Religious Store for Kids Get 10% off on orders above 999/- Home About Us Collections Products Poster Printables Blogs Contact Log in Instagram Home About Us Collections Products Poster Printables Blogs Contact Search Log in Cart Item added to your cart View cart Check out Continue shopping Privacy policy Last updated: 15/04/2024 This Privacy Policy describes how Devom Kids (the \"Site\", \"we\", \"us\", or \"our\") collects, uses, and discloses your personal information when you visit, use our services, or make a purchase from devomkids.com (the \"Site\")"
      },
      {
        "type": "CU",
        "url": "https://devomkids.com/pages/contact",
        "imglink": "screenshot_failed",
        "text": "Contact Us \u2013 Devom Kids Skip to content Welcome to our store India's First Religious Store for Kids Get 10% off on orders above 999/- Home About Us Collections Products Poster Printables Blogs Contact Log in Instagram Home About Us Collections Products Poster Printables Blogs Contact Search Log in Cart Item added to your cart View cart Check out Continue shopping Contact Us You may contact us using the information below: Merchant Legal entity name: Devom Registered Address: 1367-1, Vrindavan Colony, Lohia Talab, Mirzapur, Uttar Pradesh, PIN: 231001 Operational Address: 1367-1, Vrindavan Colony, Lohia Talab, Mirzapur, Uttar Pradesh, PIN: 231001"
      },
      {
        "type": "SD",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "FB",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "IG",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "YT",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "LI",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "PT",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "RAC",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "X",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      }
    ],
    "org_id": 1
  }
}
[2025-07-29 17:35:55,941: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:55][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🚀 WEBHOOK REQUEST SENDING - Enhanced Policy Service
{
  "url": "https://bffapi.biztel.ai/api/policy/results",
  "method": "PATCH",
  "timeout": 30,
  "request_headers": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "payload_size_kb": 3.13,
  "curl_equivalent": "curl -X PATCH 'https://bffapi.biztel.ai/api/policy/results' -H 'X-API-KEY: 12345678' -H 'Content-Type: application/json' -d '{\"website\": \"https://devomkids.com/\", \"scrapeRequestUuid\": \"cffbff75-39a0-455a-97e2-609dcef74a90\", \"createdDate\": \"2025-07-29T17:35:55.941606Z\", \"status\": \"COMPLETED\", \"policies\": [{\"type\": \"TNC\", \"url\": \"https://devomkids.com/policies/terms-of-service\", \"imglink\": \"screenshot_failed\", \"text\": \"Terms of service \\u2013 Devom Kids Skip to content Welcome to our store India\\'s First Religious Store for Kids Get 10% off on orders above 999/- Home About Us Collections Products Poster Printables Blogs Contact Log in Instagram Home About Us Collections Products Poster Printables Blogs Contact Search Log in Cart Item added to your cart View cart Check out Continue shopping Terms of service OVERVIEW This website is operated by Devom Kids. Throughout the site, the terms \\u201cwe\\u201d, \\u201cus\\u201d and \\u201cour\\u201d refer to Devom Kids. Devom Kids offers this website, including all information, tools and Services available from this\"}, {\"type\": \"PP\", \"url\": \"https://devomkids.com/policies/privacy-policy\", \"imglink\": \"screenshot_failed\", \"text\": \"Privacy policy \\u2013 Devom Kids Skip to content Welcome to our store India\\'s First Religious Store for Kids Get 10% off on orders above 999/- Home About Us Collections Products Poster Printables Blogs Contact Log in Instagram Home About Us Collections Products Poster Printables Blogs Contact Search Log in Cart Item added to your cart View cart Check out Continue shopping Privacy policy Last updated: 15/04/2024 This Privacy Policy describes how Devom Kids (the \\\"Site\\\", \\\"we\\\", \\\"us\\\", or \\\"our\\\") collects, uses, and discloses your personal information when you visit, use our services, or make a purchase from devomkids.com (the \\\"Site\\\")\"}, {\"type\": \"CU\", \"url\": \"https://devomkids.com/pages/contact\", \"imglink\": \"screenshot_failed\", \"text\": \"Contact Us \\u2013 Devom Kids Skip to content Welcome to our store India\\'s First Religious Store for Kids Get 10% off on orders above 999/- Home About Us Collections Products Poster Printables Blogs Contact Log in Instagram Home About Us Collections Products Poster Printables Blogs Contact Search Log in Cart Item added to your cart View cart Check out Continue shopping Contact Us You may contact us using the information below: Merchant Legal entity name: Devom Registered Address: 1367-1, Vrindavan Colony, Lohia Talab, Mirzapur, Uttar Pradesh, PIN: 231001 Operational Address: 1367-1, Vrindavan Colony, Lohia Talab, Mirzapur, Uttar Pradesh, PIN: 231001\"}, {\"type\": \"SD\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"FB\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"IG\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"YT\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"LI\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"PT\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"RAC\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"X\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}], \"org_id\": 1}'",
  "timestamp": "2025-07-29T17:35:55.941868"
}
[2025-07-29 17:35:55,942: DEBUG/ForkPoolWorker-7] Starting new HTTPS connection (1): bffapi.biztel.ai:443
[2025-07-29 17:35:56,099: DEBUG/ForkPoolWorker-7] https://bffapi.biztel.ai:443 "PATCH /api/policy/results HTTP/1.1" 404 None
[2025-07-29 17:35:56,100: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 📥 WEBHOOK RESPONSE RECEIVED - Enhanced Policy Service
{
  "status_code": 404,
  "success": false,
  "response_text": "{\"errorCode\":\"NO_SCRAPE_REQUEST_FOUND\",\"errorMessage\":\"An associated scrape request was not found\",\"csvLineNum\":0}",
  "response_headers": {
    "Server": "nginx/1.22.1",
    "Date": "Tue, 29 Jul 2025 12:05:56 GMT",
    "Content-Type": "application/json",
    "Transfer-Encoding": "chunked",
    "Connection": "keep-alive",
    "Vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers",
    "X-Content-Type-Options": "nosniff",
    "X-XSS-Protection": "0",
    "Cache-Control": "no-cache, no-store, max-age=0, must-revalidate",
    "Pragma": "no-cache",
    "Expires": "0",
    "X-Frame-Options": "DENY"
  },
  "response_size_bytes": 114,
  "response_size_kb": 0.11,
  "url": "https://bffapi.biztel.ai/api/policy/results",
  "payload_size_kb": 3.13,
  "request_duration_ms": 157.52,
  "timestamp": "2025-07-29T17:35:56.100100"
}
[2025-07-29 17:35:56,100: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] ERROR: ❌ WEBHOOK FAILED - Enhanced Policy Service
{
  "error": "{'status_code': 404, 'response': '{\"errorCode\":\"NO_SCRAPE_REQUEST_FOUND\",\"errorMessage\":\"An associated scrape request was not found\",\"csvLineNum\":0}', 'response_headers': {'Server': 'nginx/1.22.1', 'Date': 'Tue, 29 Jul 2025 12:05:56 GMT', 'Content-Type': 'application/json', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Vary': 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers', 'X-Content-Type-Options': 'nosniff', 'X-XSS-Protection': '0', 'Cache-Control': 'no-cache, no-store, max-age=0, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0', 'X-Frame-Options': 'DENY'}, 'curl_command': 'curl -X PATCH \\'https://bffapi.biztel.ai/api/policy/results\\' -H \\'X-API-KEY: 12345678\\' -H \\'Content-Type: application/json\\' -d \\'{\"website\": \"https://devomkids.com/\", \"scrapeRequestUuid\": \"cffbff75-39a0-455a-97e2-609dcef74a90\", \"createdDate\": \"2025-07-29T17:35:55.941606Z\", \"status\": \"COMPLETED\", \"policies\": [{\"type\": \"TNC\", \"url\": \"https://devomkids.com/policies/terms-of-service\", \"imglink\": \"screenshot_failed\", \"text\": \"Terms of service \\\\u2013 Devom Kids Skip to content Welcome to our store India\\\\\\'s First Religious Store for Kids Get 10% off on orders above 999/- Home About Us Collections Products Poster Printables Blogs Contact Log in Instagram Home About Us Collections Products Poster Printables Blogs Contact Search Log in Cart Item added to your cart View cart Check out Continue shopping Terms of service OVERVIEW This website is operated by Devom Kids. Throughout the site, the terms \\\\u201cwe\\\\u201d, \\\\u201cus\\\\u201d and \\\\u201cour\\\\u201d refer to Devom Kids. Devom Kids offers this website, including all information, tools and Services available from this\"}, {\"type\": \"PP\", \"url\": \"https://devomkids.com/policies/privacy-policy\", \"imglink\": \"screenshot_failed\", \"text\": \"Privacy policy \\\\u2013 Devom Kids Skip to content Welcome to our store India\\\\\\'s First Religious Store for Kids Get 10% off on orders above 999/- Home About Us Collections Products Poster Printables Blogs Contact Log in Instagram Home About Us Collections Products Poster Printables Blogs Contact Search Log in Cart Item added to your cart View cart Check out Continue shopping Privacy policy Last updated: 15/04/2024 This Privacy Policy describes how Devom Kids (the \\\\\"Site\\\\\", \\\\\"we\\\\\", \\\\\"us\\\\\", or \\\\\"our\\\\\") collects, uses, and discloses your personal information when you visit, use our services, or make a purchase from devomkids.com (the \\\\\"Site\\\\\")\"}, {\"type\": \"CU\", \"url\": \"https://devomkids.com/pages/contact\", \"imglink\": \"screenshot_failed\", \"text\": \"Contact Us \\\\u2013 Devom Kids Skip to content Welcome to our store India\\\\\\'s First Religious Store for Kids Get 10% off on orders above 999/- Home About Us Collections Products Poster Printables Blogs Contact Log in Instagram Home About Us Collections Products Poster Printables Blogs Contact Search Log in Cart Item added to your cart View cart Check out Continue shopping Contact Us You may contact us using the information below: Merchant Legal entity name: Devom Registered Address: 1367-1, Vrindavan Colony, Lohia Talab, Mirzapur, Uttar Pradesh, PIN: 231001 Operational Address: 1367-1, Vrindavan Colony, Lohia Talab, Mirzapur, Uttar Pradesh, PIN: 231001\"}, {\"type\": \"SD\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"FB\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"IG\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"YT\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"LI\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"PT\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"RAC\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"X\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}], \"org_id\": 1}\\'', 'webhook_url': 'https://bffapi.biztel.ai/api/policy/results', 'payload_size_kb': 3.13}"
}
[2025-07-29 17:35:56,100: WARNING/ForkPoolWorker-7] Traceback for analysis cffbff75-39a0-455a-97e2-609dcef74a90:
[2025-07-29 17:35:56,100: WARNING/ForkPoolWorker-7] Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-29 17:35:56,100: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ✅ RESULT: Webhook notification sent
[2025-07-29 17:35:56,100: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 
[2025-07-29 17:35:56,101: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 📊 EXTRACTION SUMMARY:
[2025-07-29 17:35:56,101: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📋 Total categories: 13
[2025-07-29 17:35:56,101: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📝 Successful text extractions: 4
[2025-07-29 17:35:56,101: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    📸 Successful screenshots: 0
[2025-07-29 17:35:56,101: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    🔗 Categories with URLs: 4
[2025-07-29 17:35:56,101: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO:    ⏱️ Processing time: 87.92s
[2025-07-29 17:35:56,101: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 
[2025-07-29 17:35:56,101: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: 🎉 ENHANCED POLICY ANALYSIS COMPLETED SUCCESSFULLY!
[2025-07-29 17:35:56,101: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: ================================================================================
[2025-07-29 17:35:56,101: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][cffbff75-39a0-455a-97e2-609dcef74a90][NO_REF] INFO: Enhanced policy analysis completed successfully
{
  "status": "COMPLETED",
  "scrape_request_ref_id": "cffbff75-39a0-455a-97e2-609dcef74a90",
  "website": "https://devomkids.com/",
  "analysis_flow": "normal",
  "reachability_percentage": 100.0,
  "categories_processed": 13,
  "processing_time": 87.91567277908325,
  "webhook_sent": false,
  "unified_classification": true
}
[2025-07-29 17:35:56,101: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][2][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Enhanced Policy Analysis task completed in 87.96 seconds with status: COMPLETED
2025-07-29 17:35:56,102 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-29 17:35:56,102: INFO/ForkPoolWorker-7] BEGIN (implicit)
2025-07-29 17:35:56,102 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
[2025-07-29 17:35:56,102: INFO/ForkPoolWorker-7] SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:35:56,102 INFO sqlalchemy.engine.Engine [cached since 87.93s ago] (2,)
[2025-07-29 17:35:56,102: INFO/ForkPoolWorker-7] [cached since 87.93s ago] (2,)
2025-07-29 17:35:56,103 INFO sqlalchemy.engine.Engine UPDATE policy_analysis_new_gemini SET completed_at=?, processing_status=? WHERE policy_analysis_new_gemini.id = ?
[2025-07-29 17:35:56,103: INFO/ForkPoolWorker-7] UPDATE policy_analysis_new_gemini SET completed_at=?, processing_status=? WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:35:56,103 INFO sqlalchemy.engine.Engine [generated in 0.00017s] ('2025-07-29T17:35:56.102972Z', 'FAILED', 2)
[2025-07-29 17:35:56,103: INFO/ForkPoolWorker-7] [generated in 0.00017s] ('2025-07-29T17:35:56.102972Z', 'FAILED', 2)
2025-07-29 17:35:56,104 INFO sqlalchemy.engine.Engine COMMIT
[2025-07-29 17:35:56,104: INFO/ForkPoolWorker-7] COMMIT
[2025-07-29 17:35:56,115: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][2][cffbff75-39a0-455a-97e2-609dcef74a90] INFO: Updated PolicyAnalysisNew record with results
[2025-07-29 17:35:56,116: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][process_cleanup][NO_REF] INFO: Running Celery task cleanup
[2025-07-29 17:35:56,159: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][process_cleanup][NO_REF] INFO: Found 16 Playwright processes to clean up
[2025-07-29 17:35:56,211: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][process_cleanup][NO_REF] INFO: Gracefully terminated process 8947
[2025-07-29 17:35:56,238: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][process_cleanup][NO_REF] INFO: Gracefully terminated process 9093
[2025-07-29 17:35:56,239: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][process_cleanup][NO_REF] INFO: Gracefully terminated process 11892
[2025-07-29 17:35:56,253: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][process_cleanup][NO_REF] INFO: Gracefully terminated process 11894
[2025-07-29 17:35:56,253: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][process_cleanup][NO_REF] WARNING: Could not clean up async tasks: There is no current event loop in thread 'MainThread'.
[2025-07-29 17:35:56,253: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][process_cleanup][NO_REF] INFO: Celery task cleanup completed
[2025-07-29 17:35:56,280: WARNING/ForkPoolWorker-7] [2025-07-29 17:35:56][process_cleanup][NO_REF] DEBUG: No Playwright processes found to clean up
[2025-07-29 17:35:56,281: INFO/ForkPoolWorker-7] Task process_policy_analysis_enhanced[c671c51c-99e7-45ef-b651-bee2092d8329] succeeded in 88.13826496799993s: {'status': 'failed', 'task_id': 'c671c51c-99e7-45ef-b651-bee2092d8329', 'analysis_id': 2, 'execution_time': 87.95524406433105}
