nohup: ignoring input
INFO:     Started server process [6061]
INFO:     Waiting for application startup.
2025-07-29 17:11:47,020 [app.main] INFO: Initializing application
2025-07-29 17:11:47,021 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:11:47,021 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:11:47,021 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("website_urls_gemini")
2025-07-29 17:11:47,021 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("website_urls_gemini")
2025-07-29 17:11:47,021 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:11:47,021 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:11:47,021 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("general_logs_gemini")
2025-07-29 17:11:47,021 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("general_logs_gemini")
2025-07-29 17:11:47,022 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:11:47,022 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:11:47,022 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-29 17:11:47,022 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-29 17:11:47,022 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:11:47,022 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:11:47,022 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-29 17:11:47,022 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-29 17:11:47,022 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:11:47,022 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:11:47,022 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scraped_urls_gemini")
2025-07-29 17:11:47,022 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("scraped_urls_gemini")
2025-07-29 17:11:47,022 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:11:47,022 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:11:47,022 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("websites_gemini")
2025-07-29 17:11:47,022 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("websites_gemini")
2025-07-29 17:11:47,022 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:11:47,022 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:11:47,022 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("social_media_analysis_gemini")
2025-07-29 17:11:47,022 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("social_media_analysis_gemini")
2025-07-29 17:11:47,022 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:11:47,022 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:11:47,022 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("policy_analysis_new_gemini")
2025-07-29 17:11:47,022 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("policy_analysis_new_gemini")
2025-07-29 17:11:47,023 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:11:47,023 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:11:47,023 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("social_media_profile_result_gemini")
2025-07-29 17:11:47,023 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("social_media_profile_result_gemini")
2025-07-29 17:11:47,023 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:11:47,023 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:11:47,023 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:11:47,023 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:11:47,023 [app.main] INFO: Database initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     127.0.0.1:53304 - "GET / HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53306 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53304 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:53304 - "GET /openapi.json HTTP/1.1" 200 OK
2025-07-29 17:13:29,433 [app.routers.policy_analysis] INFO: Storing URLs for policy analysis: cffbff75-39a0-455a-97e2-609dcef74a90
2025-07-29 17:13:29,436 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:13:29,436 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:13:29,642 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 17:13:29,642 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 17:13:29,643 INFO sqlalchemy.engine.Engine [generated in 0.00049s] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
2025-07-29 17:13:29,643 [sqlalchemy.engine.Engine] INFO: [generated in 0.00049s] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
2025-07-29 17:13:29,670 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,670 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,671 INFO sqlalchemy.engine.Engine [generated in 0.00084s (insertmanyvalues) 1/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,671 [sqlalchemy.engine.Engine] INFO: [generated in 0.00084s (insertmanyvalues) 1/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,672 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,672 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,672 INFO sqlalchemy.engine.Engine [insertmanyvalues 2/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/pages/about-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,672 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 2/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/pages/about-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,672 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,672 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,672 INFO sqlalchemy.engine.Engine [insertmanyvalues 3/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/geeta-gyan-cards-for-kids', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,672 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 3/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/geeta-gyan-cards-for-kids', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,672 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,672 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,672 INFO sqlalchemy.engine.Engine [insertmanyvalues 4/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/cart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,672 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 4/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/cart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,673 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,673 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,673 INFO sqlalchemy.engine.Engine [insertmanyvalues 5/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/policies/privacy-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,673 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 5/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/policies/privacy-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,673 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,673 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,673 INFO sqlalchemy.engine.Engine [insertmanyvalues 6/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,673 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 6/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,673 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,673 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,673 INFO sqlalchemy.engine.Engine [insertmanyvalues 7/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/collections', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,673 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 7/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/collections', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,674 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,674 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,674 INFO sqlalchemy.engine.Engine [insertmanyvalues 8/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/pages/contact', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,674 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 8/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/pages/contact', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,674 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,674 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,674 INFO sqlalchemy.engine.Engine [insertmanyvalues 9/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/high-contrast-hindu-god-and-goddess-flash-cards-visual-learning-20-cards', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,674 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 9/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/high-contrast-hindu-god-and-goddess-flash-cards-visual-learning-20-cards', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,674 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,674 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,674 INFO sqlalchemy.engine.Engine [insertmanyvalues 10/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/hindu-mythology-activity-tracing-folder-for-kids', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,674 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 10/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/hindu-mythology-activity-tracing-folder-for-kids', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,674 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,674 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,674 INFO sqlalchemy.engine.Engine [insertmanyvalues 11/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/navdurga-coloring-book-a-divine-journey-for-kids#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,674 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 11/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/navdurga-coloring-book-a-divine-journey-for-kids#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,674 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,674 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,674 INFO sqlalchemy.engine.Engine [insertmanyvalues 12/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/#ProductInfo-template--23909988073752__featured_product_dpeLb7', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,674 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 12/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/#ProductInfo-template--23909988073752__featured_product_dpeLb7', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,675 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,675 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,675 INFO sqlalchemy.engine.Engine [insertmanyvalues 13/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/ramayan-characters-cards-for-kids', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,675 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 13/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/ramayan-characters-cards-for-kids', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,675 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,675 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,675 INFO sqlalchemy.engine.Engine [insertmanyvalues 14/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/bal-ramayan-story-book', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,675 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 14/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/bal-ramayan-story-book', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,675 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,675 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,675 INFO sqlalchemy.engine.Engine [insertmanyvalues 15/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/pages/shipping-delivery-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,675 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 15/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/pages/shipping-delivery-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,675 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,675 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,675 INFO sqlalchemy.engine.Engine [insertmanyvalues 16/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/pages/privacy-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,675 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 16/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/pages/privacy-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,675 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,675 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,675 INFO sqlalchemy.engine.Engine [insertmanyvalues 17/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/ramayan-combo', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,675 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 17/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/ramayan-combo', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,676 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,676 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,676 INFO sqlalchemy.engine.Engine [insertmanyvalues 18/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/cdn', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,676 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 18/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/cdn', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,676 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,676 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,676 INFO sqlalchemy.engine.Engine [insertmanyvalues 19/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/activity#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,676 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 19/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/activity#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,676 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,676 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,676 INFO sqlalchemy.engine.Engine [insertmanyvalues 20/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/shri-krishna-colouring-book-for-kids-21-names-of-krishna#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,676 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 20/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/shri-krishna-colouring-book-for-kids-21-names-of-krishna#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,676 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,676 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,676 INFO sqlalchemy.engine.Engine [insertmanyvalues 21/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/blogs/hinduism/how-kids-can-learn-mantras-with-colorful-flashcards', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,676 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 21/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/blogs/hinduism/how-kids-can-learn-mantras-with-colorful-flashcards', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,676 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,676 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,676 INFO sqlalchemy.engine.Engine [insertmanyvalues 22/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/blogs/hinduism/mahakumbh-mela-144-years-later-the-rare-celestial-and-spiritual-phenomenon', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,676 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 22/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/blogs/hinduism/mahakumbh-mela-144-years-later-the-rare-celestial-and-spiritual-phenomenon', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,676 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,676 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,677 INFO sqlalchemy.engine.Engine [insertmanyvalues 23/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/dashavatar-book-and-wooden-dashavatar-peg-dolls-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,677 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 23/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/dashavatar-book-and-wooden-dashavatar-peg-dolls-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,677 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,677 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,677 INFO sqlalchemy.engine.Engine [insertmanyvalues 24/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/collections/6-and-above-age-group', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,677 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 24/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/collections/6-and-above-age-group', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,677 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,677 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,677 INFO sqlalchemy.engine.Engine [insertmanyvalues 25/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/policies/terms-of-service', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,677 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 25/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/policies/terms-of-service', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,677 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,677 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,677 INFO sqlalchemy.engine.Engine [insertmanyvalues 26/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/blogs/hinduism', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,677 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 26/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/blogs/hinduism', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,677 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,677 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,677 INFO sqlalchemy.engine.Engine [insertmanyvalues 27/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/hindu-mythology-activity-folder-for-kids-level-1#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,677 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 27/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/hindu-mythology-activity-folder-for-kids-level-1#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,677 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,677 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,678 INFO sqlalchemy.engine.Engine [insertmanyvalues 28/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/hinduism-symbol-flash-cards-for-kids-20-cards', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,678 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 28/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/hinduism-symbol-flash-cards-for-kids-20-cards', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,678 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,678 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,678 INFO sqlalchemy.engine.Engine [insertmanyvalues 29/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/little-rituals-daily-routine-cards', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,678 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 29/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/little-rituals-daily-routine-cards', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,678 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,678 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,678 INFO sqlalchemy.engine.Engine [insertmanyvalues 30/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/pages/cancellation-refund-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,678 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 30/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/pages/cancellation-refund-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,678 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,678 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,678 INFO sqlalchemy.engine.Engine [insertmanyvalues 31/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/policies/contact-information', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,678 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 31/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/policies/contact-information', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,678 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,678 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,678 INFO sqlalchemy.engine.Engine [insertmanyvalues 32/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'http://devomkids.com/cdn/shop/files/Devom_Logo.png?height=628&pad_color=ffffff&v=1712424370&width=1200', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,678 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 32/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'http://devomkids.com/cdn/shop/files/Devom_Logo.png?height=628&pad_color=ffffff&v=1712424370&width=1200', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,679 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,679 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,679 INFO sqlalchemy.engine.Engine [insertmanyvalues 33/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/combo-for-kids', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,679 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 33/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/combo-for-kids', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,679 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,679 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,679 INFO sqlalchemy.engine.Engine [insertmanyvalues 34/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/search', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,679 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 34/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/search', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,679 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,679 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,679 INFO sqlalchemy.engine.Engine [insertmanyvalues 35/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/blogs/hinduism/ramayana-flashcards-a-fun-way-to-teach-kids-mythology', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,679 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 35/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/blogs/hinduism/ramayana-flashcards-a-fun-way-to-teach-kids-mythology', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,679 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,679 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,679 INFO sqlalchemy.engine.Engine [insertmanyvalues 36/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/pages/terms-conditions', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,679 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 36/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/pages/terms-conditions', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,679 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,679 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,679 INFO sqlalchemy.engine.Engine [insertmanyvalues 37/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/collections/3-6-age-group', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,679 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 37/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/collections/3-6-age-group', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,679 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,679 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,680 INFO sqlalchemy.engine.Engine [insertmanyvalues 38/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/little-rituals-daily-routine-cards#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,680 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 38/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/little-rituals-daily-routine-cards#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,680 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,680 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,680 INFO sqlalchemy.engine.Engine [insertmanyvalues 39/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/collections/0-3-agr-group', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,680 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 39/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/collections/0-3-agr-group', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,680 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,680 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,680 INFO sqlalchemy.engine.Engine [insertmanyvalues 40/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/hindu-mythology-activity-tracing-folder-for-kids#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,680 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 40/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/hindu-mythology-activity-tracing-folder-for-kids#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,680 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,680 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,680 INFO sqlalchemy.engine.Engine [insertmanyvalues 41/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/alphabets-based-on-hinduism-and-navdurga-book-combo-for-kids#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,680 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 41/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/alphabets-based-on-hinduism-and-navdurga-book-combo-for-kids#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,680 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,680 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,680 INFO sqlalchemy.engine.Engine [insertmanyvalues 42/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/cdn/shop/files/Devom_Logo.png?height=628&pad_color=ffffff&v=1712424370&width=1200', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,680 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 42/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/cdn/shop/files/Devom_Logo.png?height=628&pad_color=ffffff&v=1712424370&width=1200', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,680 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,680 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,680 INFO sqlalchemy.engine.Engine [insertmanyvalues 43/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/collections/printable-posters', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,680 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 43/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/collections/printable-posters', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,681 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,681 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,681 INFO sqlalchemy.engine.Engine [insertmanyvalues 44/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,681 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 44/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,681 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,681 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,681 INFO sqlalchemy.engine.Engine [insertmanyvalues 45/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/18-puraan-and-dashavtar-book-combo-for-kids', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,681 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 45/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/18-puraan-and-dashavtar-book-combo-for-kids', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,681 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,681 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,681 INFO sqlalchemy.engine.Engine [insertmanyvalues 46/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/collections/all', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,681 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 46/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/collections/all', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,681 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,681 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,681 INFO sqlalchemy.engine.Engine [insertmanyvalues 47/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/policies/refund-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,681 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 47/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/policies/refund-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,681 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,681 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,681 INFO sqlalchemy.engine.Engine [insertmanyvalues 48/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/#MainContent', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,681 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 48/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/#MainContent', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,681 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,681 [sqlalchemy.engine.Engine] INFO: INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 17:13:29,681 INFO sqlalchemy.engine.Engine [insertmanyvalues 49/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/18-puraan-and-dashavtar-book-combo-for-kids#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,681 [sqlalchemy.engine.Engine] INFO: [insertmanyvalues 49/49 (ordered; batch not supported)] ('cffbff75-39a0-455a-97e2-609dcef74a90', 'https://devomkids.com/', 'https://devomkids.com/products/18-puraan-and-dashavtar-book-combo-for-kids#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 17:13:29,683 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:13:29,683 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:13:29,687 [app.utils.website_url_processor] INFO: Successfully stored 49 URLs for scrape_request_ref_id: cffbff75-39a0-455a-97e2-609dcef74a90
2025-07-29 17:13:29,689 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:13:29,689 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:13:29,695 INFO sqlalchemy.engine.Engine INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 17:13:29,695 [sqlalchemy.engine.Engine] INFO: INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 17:13:29,695 INFO sqlalchemy.engine.Engine [generated in 0.00075s] ('https://devomkids.com/', 'cffbff75-39a0-455a-97e2-609dcef74a90', 'normal', None, 0, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, 'PENDING', '2025-07-29T17:13:29.687862Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-29 17:13:29,695 [sqlalchemy.engine.Engine] INFO: [generated in 0.00075s] ('https://devomkids.com/', 'cffbff75-39a0-455a-97e2-609dcef74a90', 'normal', None, 0, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, 'PENDING', '2025-07-29T17:13:29.687862Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-29 17:13:29,696 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:13:29,696 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:13:29,702 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:13:29,702 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:13:29,709 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:13:29,709 [sqlalchemy.engine.Engine] INFO: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:13:29,709 INFO sqlalchemy.engine.Engine [generated in 0.00044s] (1,)
2025-07-29 17:13:29,709 [sqlalchemy.engine.Engine] INFO: [generated in 0.00044s] (1,)
2025-07-29 17:13:29,711 [app.routers.policy_analysis] INFO: Queuing enhanced policy analysis task for analysis_id: 1
2025-07-29 17:13:29,808 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:13:29,808 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     127.0.0.1:35698 - "POST /policy-analysis/ HTTP/1.1" 200 OK
