nohup: ignoring input
INFO:     Started server process [10375]
INFO:     Waiting for application startup.
2025-07-29 17:30:47,099 [app.main] INFO: Initializing application
2025-07-29 17:30:47,099 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:30:47,099 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:30:47,099 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("website_urls_gemini")
2025-07-29 17:30:47,099 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("website_urls_gemini")
2025-07-29 17:30:47,099 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:30:47,099 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:30:47,100 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("general_logs_gemini")
2025-07-29 17:30:47,100 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("general_logs_gemini")
2025-07-29 17:30:47,100 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:30:47,100 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:30:47,100 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-29 17:30:47,100 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-29 17:30:47,100 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:30:47,100 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:30:47,100 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-29 17:30:47,100 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-29 17:30:47,100 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:30:47,100 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:30:47,100 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scraped_urls_gemini")
2025-07-29 17:30:47,100 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("scraped_urls_gemini")
2025-07-29 17:30:47,100 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:30:47,100 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:30:47,101 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("websites_gemini")
2025-07-29 17:30:47,101 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("websites_gemini")
2025-07-29 17:30:47,101 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:30:47,101 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:30:47,101 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("social_media_analysis_gemini")
2025-07-29 17:30:47,101 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("social_media_analysis_gemini")
2025-07-29 17:30:47,101 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:30:47,101 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:30:47,101 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("policy_analysis_new_gemini")
2025-07-29 17:30:47,101 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("policy_analysis_new_gemini")
2025-07-29 17:30:47,101 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:30:47,101 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:30:47,101 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("social_media_profile_result_gemini")
2025-07-29 17:30:47,101 [sqlalchemy.engine.Engine] INFO: PRAGMA main.table_info("social_media_profile_result_gemini")
2025-07-29 17:30:47,101 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 17:30:47,101 [sqlalchemy.engine.Engine] INFO: [raw sql] ()
2025-07-29 17:30:47,101 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:30:47,101 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:30:47,101 [app.main] INFO: Database initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     127.0.0.1:41882 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:41882 - "GET /openapi.json HTTP/1.1" 200 OK
2025-07-29 17:34:27,897 [app.routers.policy_analysis] INFO: Storing URLs for policy analysis: cffbff75-39a0-455a-97e2-609dcef74a90
2025-07-29 17:34:27,899 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:34:27,899 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:34:28,045 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 17:34:28,045 [sqlalchemy.engine.Engine] INFO: SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 17:34:28,045 INFO sqlalchemy.engine.Engine [generated in 0.00029s] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
2025-07-29 17:34:28,045 [sqlalchemy.engine.Engine] INFO: [generated in 0.00029s] ('cffbff75-39a0-455a-97e2-609dcef74a90',)
2025-07-29 17:34:28,057 [app.utils.website_url_processor] INFO: URLs already exist for scrape_request_ref_id: cffbff75-39a0-455a-97e2-609dcef74a90, count: 49
2025-07-29 17:34:28,064 INFO sqlalchemy.engine.Engine INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 17:34:28,064 [sqlalchemy.engine.Engine] INFO: INSERT INTO policy_analysis_new_gemini (website, scrape_request_ref_id, analysis_flow_used, reachability_percentage, total_urls_processed, home_page_url, home_page_text, home_page_screenshot, returns_cancellation_exchange_url, returns_cancellation_exchange_text, returns_cancellation_exchange_screenshot, privacy_policy_url, privacy_policy_text, privacy_policy_screenshot, terms_and_condition_url, terms_and_condition_text, terms_and_condition_screenshot, shipping_delivery_url, shipping_delivery_text, shipping_delivery_screenshot, contact_us_url, contact_us_text, contact_us_screenshot, about_us_url, about_us_text, about_us_screenshot, instagram_url, instagram_text, instagram_screenshot, youtube_url, youtube_text, youtube_screenshot, facebook_url, facebook_text, facebook_screenshot, twitter_url, twitter_text, twitter_screenshot, linkedin_url, linkedin_text, linkedin_screenshot, pinterest_url, pinterest_text, pinterest_screenshot, x_url, x_text, x_screenshot, result_status, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 17:34:28,064 INFO sqlalchemy.engine.Engine [generated in 0.00050s] ('https://devomkids.com/', 'cffbff75-39a0-455a-97e2-609dcef74a90', 'normal', None, 0, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, 'PENDING', '2025-07-29T17:34:28.058182Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-29 17:34:28,064 [sqlalchemy.engine.Engine] INFO: [generated in 0.00050s] ('https://devomkids.com/', 'cffbff75-39a0-455a-97e2-609dcef74a90', 'normal', None, 0, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, 'PENDING', '2025-07-29T17:34:28.058182Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-29 17:34:28,065 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 17:34:28,065 [sqlalchemy.engine.Engine] INFO: COMMIT
2025-07-29 17:34:28,068 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 17:34:28,068 [sqlalchemy.engine.Engine] INFO: BEGIN (implicit)
2025-07-29 17:34:28,071 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:34:28,071 [sqlalchemy.engine.Engine] INFO: SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = ?
2025-07-29 17:34:28,072 INFO sqlalchemy.engine.Engine [generated in 0.00024s] (2,)
2025-07-29 17:34:28,072 [sqlalchemy.engine.Engine] INFO: [generated in 0.00024s] (2,)
2025-07-29 17:34:28,072 [app.routers.policy_analysis] INFO: Queuing enhanced policy analysis task for analysis_id: 2
2025-07-29 17:34:28,141 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 17:34:28,141 [sqlalchemy.engine.Engine] INFO: ROLLBACK
INFO:     127.0.0.1:41882 - "POST /policy-analysis/ HTTP/1.1" 200 OK
