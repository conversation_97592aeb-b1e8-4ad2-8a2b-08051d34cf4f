# WebReview_DS_API_24Jun - URL Classification System

A system that processes URLs from various sources (JSON files, databases, or direct lists) and classifies them into categories using GPT models. This is the DS API Codebase for policy analysis features.

## Features

- Load URLs from JSON files or direct lists
- Classify URLs into policy-related categories (privacy policy, terms and conditions, etc.)
- Configurable to use different GPT models (OpenAI or Gemini)
- SQLite database integration for storage
- Comprehensive logging
- Batch processing to handle large URL collections
- **Automatic token limiting** to ensure GPT API calls stay within limits (90K tokens)
- Screenshot capture for policy pages
- Social media URL detection and processing

## Setup

1. Clone the repository
2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
4. Set up environment variables (see `.env` file for reference)

## Database Initialization

Run the initialization script to set up the database:

```bash
python init_database.py
```

## Usage

### Classifying URLs from a JSON file

```python
from app.services.url_classification import classify_urls_from_json
from app.utils.logger import ConsoleLogger

# Create a logger
logger = ConsoleLogger("classification")

# Classify URLs from a JSON file
classified_urls = classify_urls_from_json("path/to/your/file.json", logger)

# Print results
for category, urls in classified_urls.items():
    if urls:  # Only show categories that have URLs
        print(f"\n{category.upper()}:")
        for url in urls:
            print(f"  - {url}")
```

### Classifying URLs from a list

```python
from app.services.url_classification import classify_urls_from_list
from app.utils.logger import ConsoleLogger

# Create a logger
logger = ConsoleLogger("classification")

# Define website and URLs
website = "https://example.com"
urls = [
    "https://example.com",
    "https://example.com/about",
    "https://example.com/contact",
    # ... more URLs
]

# Classify URLs
classified_urls = classify_urls_from_list(website, urls, logger)

# Print results
for category, urls in classified_urls.items():
    if urls:
        print(f"\n{category.upper()}:")
        for url in urls:
            print(f"  - {url}")
```

## Token Limiting

The system automatically handles large URL collections by:

1. Estimating token usage for each URL
2. Tracking cumulative token count
3. Limiting the number of URLs sent to GPT to stay within the 90K token limit
4. Preserving original URL indices for accurate mapping of results

This ensures that the system can process large datasets without API errors while maintaining classification accuracy.

## Testing

Run the tests to verify functionality:

```bash
# Run basic classification test
python test_classification.py

# Test token limiting with a large dataset
python test_token_limits.py
```

## API Keys

Set up your API keys in `.env`:
- `OPENAI_API_KEY` - Your OpenAI API key
- `GEMINI_API_KEY` - Your Google Gemini API key (optional)

## Categories

The system classifies URLs into the following categories:

- `home_page`: Main landing page
- `about_us`: Company information
- `terms_and_condition`: Terms of service
- `returns_cancellation_exchange`: Return and refund policies
- `privacy_policy`: Privacy information
- `shipping_delivery`: Shipping policies
- `contact_us`: Contact information
- `products`: Product pages (limited to 3)
- `services`: Service pages (limited to 3)
- `catalogue`: Main product/service catalog

## Results Example

After running the full classification test on the Lily Belle store dataset:
- 143 URLs processed
- 135 URLs successfully classified
- 9 out of 10 categories identified
- Distribution: 120 product pages, 6 category pages, and various policy/information pages

## Implementation Notes

The URL classification system implements several key features to ensure reliable and efficient operation:

1. **Token Management**: Automatically tracks and limits token usage to stay within GPT model constraints (90K tokens)
2. **Batch Processing**: Divides large URL collections into manageable batches for processing
3. **Error Handling**: Implements retry logic and comprehensive error handling for API calls
4. **Flexible Input**: Supports multiple input formats (JSON files, URL lists, database records)
5. **Detailed Logging**: Provides comprehensive logging of all operations for debugging and analysis

The system is designed to be robust against API failures, token limit issues, and various edge cases that can occur when processing real-world URL datasets.
