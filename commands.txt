for celery start



for uvicorn start

nohup uvicorn app.main:app --host 0.0.0.0 --port 8000 > logs/server_619.log 2>&1 &

nohup celery -A app.tasks.celery_tasks worker -l debug -Q celery,risky_queue,mcc_queue,policy_queue,general_queue > logs/worker_619.log 2>&1 &

**************************


nohup celery -A app.tasks.celery_tasks flower > flower.log 2>&1 &


api key: 12345678

Implement complete Policy Analysis Service flow with two distinct processing paths:

**first understand the current code how different it is and then make targetteed changes**

**CASE 1: Normal Flow (Primary Path)**
- Accept policy analysis requests using the defined input format (as defined in db_models.py)
- Check if analysis already exists for the scrape_request_ref_id; if yes, return existing results from database
- If new request, perform URL classification using existing service functions for both soft and hard classification
- **CRITICAL: Do not modify classification prompts or input formats - use existing functions**
- Focus on these priority categories only: home_page, terms_and_condition, returns_cancellation_exchange, privacy_policy, shipping_delivery, contact_us
- **Exclude social media URLs from priority processing** - for social media URLs (IG, FB, YT, LI, TW, PT, X), use soft classification output as-is without modification
- Normal flow trigger condition: Continue if ≥50% of priority URLs are reachable AND no URLs appear in "Unreachable_via_tool" category
- **IMMEDIATE FALLBACK TRIGGER**: If ANY single URL appears in "Unreachable_via_tool" category, immediately switch to backup flow
- For normal flow processing: Extract both text and images for each reachable priority URL, then prepare individual PATCH webhook calls

**CASE 2: Backup Flow (Fallback Path)**
- Trigger conditions: >50% of priority URLs in "urls_not_reachable" OR any URL in "Unreachable_via_tool"
- Use soft classification output to select one URL per category (avoiding duplicates)
- Processing priority: Text extraction (HIGH priority) + Screenshot capture (LOW priority)
- For text classification: Crop extracted text to <90k tokens and send to Gemini for hard classification (one URL at a time)
- Pass both extracted text AND captured images to Gemini for classification

**URL Selection and Prioritization Rules:**
- When soft classification returns multiple URLs for products/catalogue, ensure priority URLs are selected first
- Priority order for URL selection: home_page, about_us, terms_and_conditions, privacy_policy, shipping_delivery, contact_us, then products/catalogue
- Select one URL from each category to avoid losing critical policy pages
- If multiple URLs match same category, keep only the first one
- Use webshare proxy as primary connection method

**Technical Requirements:**
- Maintain existing PATCH webhook payload format (no changes to webhook structure)
- Use existing database schema without modifications
- Preserve all error handling and retry mechanisms from existing services
- Route all logs to Celery worker logs (not server.log)
- Handle social media URLs with conditional popup handling (close_popup=true for social media)

**Category Priority Mapping:**
RAC (returns_cancellation_exchange), TNC (terms_and_condition), PP (privacy_policy), SD (shipping_delivery), CU (contact_us), IG (Instagram), FB (Facebook), YT (YouTube), LI (LinkedIn), TW (Twitter), PT (Pinterest), X (X/Twitter)

{
"type":"AU"
"url":"string"
"imglink": "string"
"text": "string"
}