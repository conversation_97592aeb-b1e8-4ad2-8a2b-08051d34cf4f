"""
Celery tasks for policy analysis processing
"""

import os
import time
import json
import asyncio
import traceback
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.celery_app import celery_app
from app.utils.logger import ConsoleLogger
from app.services.policy_analysis_enhanced_service import PolicyAnalysisEnhancedService
from sqlmodel import Session, select

def _truncate_text_to_words(text: str, max_words: int) -> str:
    """
    Truncate text to a maximum number of words for API compatibility

    Args:
        text (str): Original text to truncate
        max_words (int): Maximum number of words to keep

    Returns:
        str: Truncated text or original if within limit
    """
    if not text or text == "insufficient data":
        return text

    words = text.split()
    if len(words) <= max_words:
        return text

    truncated = " ".join(words[:max_words])
    return truncated

# Policy analysis tasks

@celery_app.task(
    bind=True,
    name="process_policy_analysis_enhanced",
    time_limit=2400,  # 40 minutes hard timeout
    soft_time_limit=2100,  # 35 minutes soft timeout
    max_retries=2,
    retry_backoff=True,
    retry_backoff_max=600,
    acks_late=True,
    track_started=True
)
def process_policy_analysis_enhanced(self, analysis_id):
    """
    Celery task to process enhanced policy analysis using new screenshot service with conditional popup handling

    Args:
        analysis_id (int): ID of the PolicyAnalysisNew record to process

    Returns:
        dict: Result of the analysis with status and details
    """
    import signal
    import asyncio
    from celery.exceptions import SoftTimeLimitExceeded
    from app.utils.process_cleanup import ProcessCleanupContext, celery_task_cleanup

    start_time = time.time()

    # Initialize logger first
    logger = ConsoleLogger(analysis_id)

    # Use process cleanup context for automatic cleanup
    with ProcessCleanupContext(cleanup_callback=celery_task_cleanup):
        try:
            logger.info(f"Starting enhanced policy analysis task for analysis_id: {analysis_id}")

            # Import required modules
            from sqlmodel import Session, select
            from app.models.db_models import PolicyAnalysisNew
            from app.database import engine

            logger.info(f"Importing required modules for enhanced policy analysis")

            # Get the PolicyAnalysisNew record from the database
            logger.info(f"Opening database session to get PolicyAnalysisNew record")
            with Session(engine) as session:
                analysis = session.get(PolicyAnalysisNew, analysis_id)
                if not analysis:
                    error_msg = f"PolicyAnalysisNew with ID {analysis_id} not found"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                # Extract values while object is still attached to session
                scrape_request_ref_id = analysis.scrape_request_ref_id
                org_id = analysis.org_id
                website = analysis.website

                logger.info(f"Found PolicyAnalysisNew record: {website}, ref_id: {scrape_request_ref_id}")

                # Update analysis status
                analysis.processing_status = "PROCESSING"
                analysis.started_at = datetime.now().isoformat() + "Z"
                session.commit()
                logger.info(f"Updated PolicyAnalysisNew status to PROCESSING")

            # Initialize the Enhanced Policy Analysis service using extracted values
            logger.info(f"Initializing Enhanced Policy Analysis service")
            policy_service = PolicyAnalysisEnhancedService(
                scrape_request_ref_id=scrape_request_ref_id,
                org_id=org_id
            )

            # Process the Enhanced Policy Analysis (includes backup flow logic and conditional popup handling)
            logger.info(f"Running Enhanced Policy Analysis with conditional popup handling")

            # Run with timeout to prevent hanging
            async def run_with_timeout():
                return await asyncio.wait_for(
                    policy_service.process_policy_analysis(),
                    timeout=2000  # 33 minutes timeout (less than soft limit)
                )

            try:
                result = asyncio.run(run_with_timeout())
            except asyncio.TimeoutError:
                logger.error("Enhanced Policy Analysis timed out after 33 minutes")
                raise Exception("Analysis timed out")

            processing_time = time.time() - start_time
            logger.info(f"Enhanced Policy Analysis task completed in {processing_time:.2f} seconds with status: {result.get('status')}")

            # Update the database with results
            with Session(engine) as session:
                analysis = session.get(PolicyAnalysisNew, analysis_id)
                if analysis:
                    if result.get("status") == "success":
                        analysis.processing_status = "COMPLETED"
                        analysis.completed_at = datetime.now().isoformat() + "Z"

                        # Update analysis flow tracking
                        if result.get("analysis_flow_used"):
                            analysis.analysis_flow_used = result["analysis_flow_used"]

                        if result.get("reachability_percentage"):
                            analysis.reachability_percentage = result["reachability_percentage"]

                        if result.get("total_urls_processed"):
                            analysis.total_urls_processed = result["total_urls_processed"]

                        # Update details if available
                        if result.get("details"):
                            analysis.details = json.dumps(result["details"])

                    else:
                        analysis.processing_status = "FAILED"
                        analysis.completed_at = datetime.now().isoformat() + "Z"
                        if result.get("error"):
                            analysis.error_message = str(result["error"])

                    session.commit()
                    logger.info(f"Updated PolicyAnalysisNew record with results")

            return {
                "status": "success" if result.get("status") == "success" else "failed",
                "task_id": self.request.id,
                "analysis_id": analysis_id,
                "execution_time": processing_time
            }

        except SoftTimeLimitExceeded:
            processing_time = time.time() - start_time
            error_msg = "Enhanced Policy Analysis task exceeded soft time limit"
            logger.error(error_msg)

            # Update database with timeout error
            try:
                with Session(engine) as session:
                    analysis = session.get(PolicyAnalysisNew, analysis_id)
                    if analysis:
                        analysis.processing_status = "FAILED"
                        analysis.completed_at = datetime.now().isoformat() + "Z"
                        analysis.error_message = error_msg
                        session.commit()
            except Exception as db_error:
                logger.error(f"Failed to update database with timeout error: {str(db_error)}")

            return {
                "status": "FAILED",
                "error": error_msg,
                "task_id": self.request.id,
                "analysis_id": analysis_id,
                "execution_time": processing_time
            }

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Error in Enhanced Policy Analysis task: {str(e)}"
            logger.error(error_msg)
            logger.error(f"Full traceback: {traceback.format_exc()}")

            # Update database with error
            try:
                with Session(engine) as session:
                    analysis = session.get(PolicyAnalysisNew, analysis_id)
                    if analysis:
                        analysis.processing_status = "FAILED"
                        analysis.completed_at = datetime.now().isoformat() + "Z"
                        analysis.error_message = error_msg
                        session.commit()
            except Exception as db_error:
                logger.error(f"Failed to update database with error: {str(db_error)}")

            return {
                "status": "FAILED",
                "error": error_msg,
                "task_id": self.request.id,
                "analysis_id": analysis_id,
                "execution_time": processing_time
            }


@celery_app.task(
    bind=True,
    name="process_policy_analysis",
    time_limit=2400,  # 40 minutes hard timeout
    soft_time_limit=2100,  # 35 minutes soft timeout
    max_retries=2,
    retry_backoff=True,
    retry_backoff_max=600,
    acks_late=True,
    track_started=True
)
def process_policy_analysis(self, analysis_id):
    """
    Celery task to process policy analysis with backup flow support

    Args:
        analysis_id (int): ID of the PolicyAnalysisNew record to process

    Returns:
        dict: Result of the analysis with status and details
    """

    logger = ConsoleLogger(analysis_id)
    logger.info(f"Starting Policy Analysis task for analysis_id: {analysis_id}")

    start_time = time.time()

    try:
        # Get the PolicyAnalysisNew record from the database
        from sqlmodel import Session, select
        from app.models.db_models import PolicyAnalysisNew
        from app.database import engine

        logger.info(f"Importing required modules for Policy Analysis")

        # Import the service here to avoid circular import
        logger.info(f"Opening database session to get PolicyAnalysisNew record")
        with Session(engine) as session:
            # Get the PolicyAnalysisNew record
            analysis = session.get(PolicyAnalysisNew, analysis_id)
            if not analysis:
                error_msg = f"PolicyAnalysisNew with ID {analysis_id} not found"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # Extract values while object is still attached to session
            scrape_request_ref_id = analysis.scrape_request_ref_id
            org_id = analysis.org_id
            website = analysis.website

            logger.info(f"Found PolicyAnalysisNew record: {website}, ref_id: {scrape_request_ref_id}")

            # Update analysis status
            analysis.processing_status = "PROCESSING"
            analysis.started_at = datetime.now().isoformat() + "Z"
            session.commit()
            logger.info(f"Updated PolicyAnalysisNew status to PROCESSING")

        # Initialize the Policy Analysis service using extracted values
        logger.info(f"Initializing Policy Analysis service")
        policy_service = PolicyAnalysisEnhancedService(
            scrape_request_ref_id=scrape_request_ref_id,
            org_id=org_id
        )

        # Process the Policy Analysis
        logger.info(f"Running Policy Analysis")
        result = asyncio.run(policy_service.process_policy_analysis())

        processing_time = time.time() - start_time
        logger.info(f"Policy Analysis task completed in {processing_time:.2f} seconds with status: {result.get('status')}")

        # Update the database with results
        with Session(engine) as session:
            analysis = session.get(PolicyAnalysisNew, analysis_id)
            if analysis:
                if result.get("status") == "success":
                    analysis.processing_status = "COMPLETED"
                    analysis.completed_at = datetime.now().isoformat() + "Z"

                    # Update analysis flow tracking
                    if result.get("analysis_flow_used"):
                        analysis.analysis_flow_used = result["analysis_flow_used"]

                    if result.get("reachability_percentage"):
                        analysis.reachability_percentage = result["reachability_percentage"]

                    if result.get("total_urls_processed"):
                        analysis.total_urls_processed = result["total_urls_processed"]

                    # Update details if available
                    if result.get("details"):
                        analysis.details = json.dumps(result["details"])

                else:
                    analysis.processing_status = "FAILED"
                    analysis.completed_at = datetime.now().isoformat() + "Z"
                    if result.get("error"):
                        analysis.error_message = str(result["error"])

                session.commit()
                logger.info(f"Updated PolicyAnalysisNew record with results")

        return {
            "status": "success" if result.get("status") == "success" else "failed",
            "task_id": self.request.id,
            "analysis_id": analysis_id,
            "execution_time": processing_time
        }

    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"Error in Policy Analysis task: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Full traceback: {traceback.format_exc()}")

        # Update database with error
        try:
            with Session(engine) as session:
                analysis = session.get(PolicyAnalysisNew, analysis_id)
                if analysis:
                    analysis.processing_status = "FAILED"
                    analysis.completed_at = datetime.now().isoformat() + "Z"
                    analysis.error_message = error_msg
                    session.commit()
        except Exception as db_error:
            logger.error(f"Failed to update database with error: {str(db_error)}")

        return {
            "status": "FAILED",
            "error": error_msg,
            "task_id": self.request.id,
            "analysis_id": analysis_id,
            "execution_time": processing_time
        }
