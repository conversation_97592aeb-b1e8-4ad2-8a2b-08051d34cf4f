"""
Task package initialization
This ensures all tasks are imported and registered with Celery
"""

# Import tasks to ensure they're registered
from app.tasks.test_task import test_task
# Import only policy-related tasks
from app.tasks.celery_tasks import (
    process_policy_analysis,
    process_policy_analysis_enhanced
)

# Export all tasks
__all__ = [
    'test_task',
    'process_policy_analysis',
    'process_policy_analysis_enhanced'
]