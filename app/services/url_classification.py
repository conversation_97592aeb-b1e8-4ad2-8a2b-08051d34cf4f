"""
URL Classification Service for Policy Analysis
"""

import asyncio
from typing import Dict, List, Tuple, Any
import pandas as pd
from sqlmodel import Session, select

from app.utils.logger import ConsoleLogger
from app.models.db_models import WebsiteUrls
from app.database import engine
from app.gpt_models.gpt_prompts import GptPromptPicker
from app.gpt_models.gemini_model_wrapper.gemeni_utils import get_optimized_gemini_response_for_task


class urlclassification_service:
    """
    URL Classification Service for policy analysis
    """
    
    def __init__(self, website: str, scrape_request_ref_id: str, org_id: str):
        """
        Initialize the URL classification service
        
        Args:
            website (str): The website URL
            scrape_request_ref_id (str): Reference ID for the scrape request
            org_id (str): Organization ID
        """
        self.website = website
        self.scrape_request_ref_id = scrape_request_ref_id
        self.org_id = org_id
        self.logger = ConsoleLogger(scrape_request_ref_id)
    
    def soft_classify_urls(self, depth_1_urls: List[str], depth_2_urls: List[str], 
                          logger: Console<PERSON>ogger, scrape_request_ref_id: str, org_id: str) -> Tuple[pd.DataFrame, Dict[str, List[str]]]:
        """
        Perform soft classification of URLs using GPT models
        
        Args:
            depth_1_urls (List[str]): URLs at depth 1
            depth_2_urls (List[str]): URLs at depth 2
            logger (ConsoleLogger): Logger instance
            scrape_request_ref_id (str): Reference ID
            org_id (str): Organization ID
            
        Returns:
            Tuple[pd.DataFrame, Dict[str, List[str]]]: DataFrame and classified URLs dictionary
        """
        try:
            logger.info("Starting soft URL classification")
            
            # Combine all URLs
            all_urls = depth_1_urls + depth_2_urls
            
            if not all_urls:
                logger.warning("No URLs provided for classification")
                return pd.DataFrame(), {}
            
            # Create URL dictionary for classification
            url_dict = {str(i): url for i, url in enumerate(all_urls, 1)}
            
            # Get soft classification prompt
            prompt = GptPromptPicker.get_gpt_4o_soft_classification_prompt(self.website, url_dict)
            
            # Get classification response from Gemini
            response = get_optimized_gemini_response_for_task(
                prompt=prompt,
                model_name="gemini-2.5-flash",
                timeout_seconds=120
            )
            
            if not response:
                logger.error("No response from Gemini for soft classification")
                return pd.DataFrame(), {}
            
            # Parse the response to extract classified URLs
            classified_urls = self._parse_classification_response(response, all_urls)
            
            # Create DataFrame for compatibility
            df_data = []
            for url in all_urls:
                category = self._find_url_category(url, classified_urls)
                df_data.append({
                    'url': url,
                    'soft_class': category,
                    'hard_class': '',
                    'scrape_request_ref_id': scrape_request_ref_id,
                    'org_id': org_id
                })
            
            output_df = pd.DataFrame(df_data)
            
            logger.info(f"Soft classification completed for {len(all_urls)} URLs")
            return output_df, classified_urls
            
        except Exception as e:
            logger.error(f"Error in soft URL classification: {str(e)}")
            return pd.DataFrame(), {}
    
    def _parse_classification_response(self, response: str, all_urls: List[str]) -> Dict[str, List[str]]:
        """
        Parse the classification response from Gemini
        
        Args:
            response (str): Raw response from Gemini
            all_urls (List[str]): List of all URLs
            
        Returns:
            Dict[str, List[str]]: Classified URLs by category
        """
        try:
            import json
            
            # Try to parse as JSON first
            if response.strip().startswith('{'):
                data = json.loads(response)
                return data
            
            # If not JSON, create a basic classification
            classified_urls = {
                "home_page": [],
                "about_us": [],
                "privacy_policy": [],
                "terms_and_condition": [],
                "returns_cancellation_exchange": [],
                "shipping_delivery": [],
                "contact_us": [],
                "instagram": [],
                "facebook": [],
                "twitter": [],
                "linkedin": [],
                "youtube": [],
                "pinterest": [],
                "x": [],
                "other": []
            }
            
            # Basic URL classification based on keywords
            for url in all_urls:
                url_lower = url.lower()
                
                if any(keyword in url_lower for keyword in ['privacy', 'policy']):
                    classified_urls["privacy_policy"].append(url)
                elif any(keyword in url_lower for keyword in ['terms', 'condition']):
                    classified_urls["terms_and_condition"].append(url)
                elif any(keyword in url_lower for keyword in ['return', 'refund', 'exchange']):
                    classified_urls["returns_cancellation_exchange"].append(url)
                elif any(keyword in url_lower for keyword in ['shipping', 'delivery']):
                    classified_urls["shipping_delivery"].append(url)
                elif any(keyword in url_lower for keyword in ['contact', 'support']):
                    classified_urls["contact_us"].append(url)
                elif any(keyword in url_lower for keyword in ['about']):
                    classified_urls["about_us"].append(url)
                elif 'instagram.com' in url_lower:
                    classified_urls["instagram"].append(url)
                elif 'facebook.com' in url_lower:
                    classified_urls["facebook"].append(url)
                elif 'twitter.com' in url_lower or 'x.com' in url_lower:
                    classified_urls["twitter"].append(url)
                elif 'linkedin.com' in url_lower:
                    classified_urls["linkedin"].append(url)
                elif 'youtube.com' in url_lower:
                    classified_urls["youtube"].append(url)
                elif 'pinterest.com' in url_lower:
                    classified_urls["pinterest"].append(url)
                else:
                    classified_urls["other"].append(url)
            
            return classified_urls
            
        except Exception as e:
            self.logger.error(f"Error parsing classification response: {str(e)}")
            return {}
    
    def _find_url_category(self, url: str, classified_urls: Dict[str, List[str]]) -> str:
        """
        Find which category a URL belongs to
        
        Args:
            url (str): The URL to find category for
            classified_urls (Dict[str, List[str]]): Classified URLs by category
            
        Returns:
            str: The category name
        """
        for category, urls in classified_urls.items():
            if url in urls:
                return category
        return "other"
