import asyncio
import time
import json
import traceback
from typing import Dict, <PERSON>, Optional, Tuple, Set, Union
from datetime import datetime
import requests
import os
import uuid
import tempfile
from base64 import b64encode

from app.utils.logger import ConsoleLogger
from app.services.screenshot.run_screenshot import capture_screenshot
# Removed UrlClassificationService import - now using existing classification results
from app.gpt_models.gemini_model_wrapper.gemeni_utils import (
    get_optimized_gemini_response_for_task
)
from app.gpt_models.gpt_prompts import GptPromptPicker
from app.database import engine
from app.models.db_models import PolicyAnalysisNew, WebsiteUrls, get_current_time
from sqlmodel import Session, select
from bs4 import BeautifulSoup
from app.services.screenshot.blob_utils import upload_to_azure_container
from app.services.screenshot.url_utils import get_text_from_url_local
from app.services.url_classification import urlclassification_service


class PolicyAnalysisEnhancedService:
    """
    Enhanced Policy Analysis Service using new screenshot service with conditional popup handling
    """
    
    def __init__(self, scrape_request_ref_id: str, org_id: str = "default"):
        """
        Initialize the enhanced policy analysis service
        
        Args:
            scrape_request_ref_id (str): Reference ID for tracking
            org_id (str): Organization ID
        """
        self.scrape_request_ref_id = scrape_request_ref_id
        self.org_id = org_id
        self.logger = ConsoleLogger(scrape_request_ref_id)
        
        # Initialize screenshot service - REMOVED since we are using run_screenshot directly
        # self.screenshot_service = FastScreenshotService(scrape_request_ref_id, "policy")
                
        # 🎯 TARGET PRIORITY CATEGORIES ONLY (RAC, TNC, PP, SD, CU, AU)
        self.required_categories = {
            "returns_cancellation_exchange",  # RAC
            "terms_and_condition",            # TNC
            "privacy_policy",                 # PP
            "shipping_delivery",              # SD
            "contact_us",                     # CU
            "about_us"                        # AU
        }
        
        # 📱 SOCIAL MEDIA TARGET CATEGORIES (IG, FB, YT, LI, TW, PT, X)
        self.social_media_categories = {
            "instagram_page": "instagram",    # IG
            "youtube_page": "youtube",        # YT
            "facebook_page": "facebook",      # FB
            "twitter_page": "twitter",        # TW
            "linkedin_page": "linkedin",      # LI
            "pinterest_page": "pinterest",    # PT
            "x_page": "x"                    # X (formerly Twitter)
        }
        
        # All categories to process (classification names)
        self.all_classification_categories = self.required_categories.union(set(self.social_media_categories.keys()))
        
        # All database field categories (using mapped names)
        self.all_db_categories = self.required_categories.union(set(self.social_media_categories.values()))
        
        # REMOVED: Social media keyword patterns - AI models handle classification
        
        # Webhook configuration - use settings
        from app.config import settings
        self.webhook_url = f"{settings.BASE_URL}/api/policy/results"
        self.webhook_headers = {
            "X-API-KEY": settings.BIZTEL_API_KEY,
            "Content-Type": "application/json"
        }
        
        self.logger.info("Enhanced Policy Analysis Service initialized", {
            "scrape_request_ref_id": scrape_request_ref_id,
            "org_id": org_id,
            "required_categories": len(self.required_categories),
            "social_media_categories": len(self.social_media_categories)
        })

    async def _capture_and_upload_screenshot(self, url: str) -> str:
        """
        Capture screenshot using run_screenshot.py and upload to Azure.
        """
        try:
            screenshot_bytes = await capture_screenshot(url)
            if not screenshot_bytes:
                self.logger.warning(f"Screenshot capture returned no data for {url}")
                return "screenshot_failed"

            with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as temp_file:
                temp_file.write(screenshot_bytes)
                temp_file_path = temp_file.name

            try:
                filename = f"{uuid.uuid4()}.png"
                azure_url = await upload_to_azure_container(temp_file_path, filename, self.logger)
                if azure_url:
                    self.logger.info(f"Successfully uploaded screenshot to Azure for {url}", {"azure_url": azure_url})
                    return azure_url
                else:
                    self.logger.error(f"Azure upload failed for {url}, no URL returned.")
                    return "screenshot_failed"
            finally:
                os.remove(temp_file_path)

        except Exception as e:
            self.logger.error(f"Error in _capture_and_upload_screenshot for {url}", error=e)
            return "screenshot_failed"

    # REMOVED: Obsolete social media detection methods
    # URL classification service now handles all classification decisions properly

    def crop_text_to_token_limit(self, text: str, max_tokens: int = 60000) -> str:
        """
        Crop text to fit within token limit for Gemini classification
        
        Args:
            text (str): Text to crop
            max_tokens (int): Maximum token limit
            
        Returns:
            str: Cropped text
        """
        try:
            # Rough estimation: 1 token ≈ 4 characters
            max_chars = max_tokens * 4
            if len(text) <= max_chars:
                return text
            
            # Crop text and add indicator
            cropped_text = text[:max_chars]
            self.logger.info(f"Text cropped from {len(text)} to {len(cropped_text)} characters")
            return cropped_text + "\n\n[TEXT TRUNCATED DUE TO LENGTH]"
            
        except Exception as e:
            self.logger.warning(f"Error cropping text: {str(e)}")
            return text[:max_chars]  # Fallback calculated to calculated character limit



    async def extract_text_robust(self, target_url: str) -> str:
        """
        Extract text content using multiple extraction methods for a specific target URL
        
        Args:
            target_url (str): Specific target URL to extract from
            
        Returns:
            str: Extracted text content or empty string if failed
        """
        try:
            self.logger.info("Starting robust text extraction", {"target_url": target_url})
            
            # Try safe text extraction first (HTTP requests first, then browser automation)
            extraction_methods = ["requests", "playwright"]
            
            for method_index, method in enumerate(extraction_methods):
                try:
                    self.logger.info(f"Attempting {method} extraction", {
                        "url": target_url,
                        "method": method,
                        "attempt": method_index + 1
                    })
                    
                    extracted_text = await self.extract_text_with_method(target_url, method)
                    
                    if extracted_text and len(extracted_text.strip()) > 50:
                        self.logger.info(f"Text extraction successful with {method}", {
                            "url": target_url,
                            "text_length": len(extracted_text),
                            "method": method
                        })
                        return extracted_text
                    else:
                        self.logger.warning(f"Text extraction with {method} returned insufficient data")
                        
                except Exception as e:
                    self.logger.warning(f"Text extraction method '{method}' failed", {
                        "url": target_url,
                        "error": str(e)
                    })
                    continue
            
            self.logger.warning(f"All text extraction methods failed for {target_url}")
            return "text_extraction_failed"
            
        except Exception as e:
            self.logger.error(f"Error in robust text extraction: {str(e)}")
            return ""

    async def extract_text_with_method(self, url: str, method: str) -> str:
        """
        Extract text using a specific method

        Args:
            url (str): URL to extract text from
            method (str): Extraction method ("requests" or "playwright")

        Returns:
            str: Extracted text or empty string if failed
        """
        try:
            if method == "playwright":
                # Use playwright-based text extraction
                extracted_text = await get_text_from_url_local(
                    url,
                    endpoint_type="policy_analysis",
                    org_id=self.org_id,
                    timeout=45
                )
                return extracted_text if extracted_text else "text_extraction_failed"

            elif method == "requests":
                # Use requests-based text extraction (simplified approach)
                try:
                    import requests
                    from bs4 import BeautifulSoup

                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }

                    response = requests.get(url, headers=headers, timeout=30)
                    response.raise_for_status()

                    soup = BeautifulSoup(response.content, 'html.parser')

                    # Remove script and style elements
                    for script in soup(["script", "style"]):
                        script.decompose()

                    # Get text content
                    text = soup.get_text()

                    # Clean up text
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    text = ' '.join(chunk for chunk in chunks if chunk)

                    return text if text and len(text.strip()) > 50 else "text_extraction_failed"

                except Exception as e:
                    self.logger.warning(f"Requests-based text extraction failed: {str(e)}")
                    return "text_extraction_failed"

            return "text_extraction_failed"

        except Exception as e:
            self.logger.warning(f"Text extraction method '{method}' failed", {"url": url, "error": str(e)})
            return ""

    # REMOVED: classify_text_with_gemini method - redundant with URL classification service
    # The URL classification service already handles text classification properly

    async def verify_category_with_gemini_image_analysis(self, text: str, screenshot_url: str, url: str, expected_category: str, website: str) -> str:
        """
        BACKUP FLOW FEATURE: Verify category mapping using both text and image analysis with Gemini
        
        This method sends both extracted text AND screenshot image to Gemini for verification
        of the category classification in backup flow scenarios.

        Args:
            text (str): Extracted text content
            screenshot_url (str): Azure blob URL of the screenshot
            url (str): Source URL
            expected_category (str): Category from soft classification
            website (str): Website URL

        Returns:
            str: Verified category or original category if verification fails
        """
        try:
            self.logger.info(f"🔍 Starting Gemini image+text verification for backup flow", {
                "url": url,
                "expected_category": expected_category,
                "has_text": len(text) > 0 if text != "text_extraction_failed" else False,
                "has_screenshot": screenshot_url != "screenshot_failed"
            })

            # Skip verification if we don't have both text and screenshot
            if text == "text_extraction_failed" or screenshot_url == "screenshot_failed":
                self.logger.warning("Skipping Gemini verification - insufficient data", {
                    "url": url,
                    "has_text": text != "text_extraction_failed",
                    "has_screenshot": screenshot_url != "screenshot_failed"
                })
                return expected_category

            # Crop text to token limit
            cropped_text = self.crop_text_to_token_limit(text, 30000)  # Reduced limit for image+text

            # Create verification prompt using existing policy text classification prompt
            verification_prompt = GptPromptPicker.get_policy_text_classification_prompt(
                cropped_text, website
            )

            # TODO: Implement Gemini image analysis
            # For now, use text-only analysis until Gemini image support is implemented
            self.logger.info("🚧 Using text-only verification (image analysis pending implementation)", {
                "url": url,
                "expected_category": expected_category
            })

            response = get_optimized_gemini_response_for_task(
                verification_prompt,
                task_type="post_text_extraction"
            )

            if response and response.strip():
                try:
                    # Parse JSON response from policy text classification prompt
                    import json
                    parsed_response = json.loads(response.strip())
                    
                    categories = parsed_response.get("category", [])
                    if categories and isinstance(categories, list):
                        # Use the first category as the verified category
                        verified_category = categories[0].lower()
                        
                        if verified_category != expected_category:
                            self.logger.info(f"🔄 Gemini verification adjusted category", {
                                "url": url,
                                "original_category": expected_category,
                                "verified_category": verified_category,
                                "reasoning": parsed_response.get("reasoning", ""),
                                "all_categories": categories
                            })
                        else:
                            self.logger.info(f"✅ Gemini verification confirmed category", {
                                "url": url,
                                "category": verified_category,
                                "reasoning": parsed_response.get("reasoning", "")
                            })
                        
                        return verified_category
                    else:
                        self.logger.warning(f"No categories found in verification response for {url}")
                        return expected_category
                        
                except json.JSONDecodeError as e:
                    self.logger.warning(f"Failed to parse verification JSON response for {url}: {str(e)}")
                    return expected_category
            else:
                self.logger.warning(f"Empty Gemini verification response for {url}")
                return expected_category

        except Exception as e:
            self.logger.error(f"Error in Gemini image+text verification: {str(e)}", {
                "url": url,
                "expected_category": expected_category
            })
            return expected_category

    async def extract_content_for_url(self, url: str, category: str) -> Tuple[str, str]:
        """
        Extract text content and capture screenshot for a URL with conditional popup handling
        """
        try:
            self.logger.info(f"Starting content extraction for {category}", {"url": url})

            # Extract text content
            extracted_text = await self.extract_text_robust(url)
            
            # For social media, text extraction is not the priority
            if category in self.social_media_categories:
                if not extracted_text or extracted_text == "text_extraction_failed":
                    extracted_text = "Social media URL - no text extraction needed"

            # Capture screenshot using the existing method
            screenshot_url = await self._capture_and_upload_screenshot(url)

            self.logger.info(f"Content extraction completed for {category}", {
                "url": url,
                "text_length": len(extracted_text),
                "screenshot_status": "success" if screenshot_url != "screenshot_failed" else "failed"
            })
            
            return extracted_text, screenshot_url

        except Exception as e:
            self.logger.error(f"Error in extract_content_for_url for {url}", error=e)
            return "text_extraction_failed", "screenshot_failed"

    async def process_hard_classification_flow(self, classified_urls: Dict[str, List[str]]) -> Dict[str, Dict[str, str]]:
        """
        Process hard classification flow - select representative URLs and extract content
        
        Args:
            classified_urls: Dictionary of classified URLs by category
            
        Returns:
            Dict containing content data by category
        """
        try:
            self.logger.info("Processing hard classification flow")
            
            # Select representative URLs
            representative_urls = self.select_representative_urls(classified_urls)
            if not representative_urls:
                self.logger.error("No representative URLs found")
                return {}
            
            self.logger.info("Representative URLs selected", {
                "categories_selected": len(representative_urls),
                "categories": list(representative_urls.keys())
            })
            
            # Extract content for all URLs
            content_data = await self.process_content_extraction(representative_urls)
            
            self.logger.info("Hard classification flow completed", {
                "categories_processed": len(content_data)
            })
            
            return content_data
            
        except Exception as e:
            self.logger.error(f"Error in hard classification flow: {str(e)}")
            return {}

    def select_representative_urls(self, classified_urls: Dict[str, List[str]]) -> Dict[str, str]:
        """
        Select one representative URL for each category following priority order
        
        Args:
            classified_urls (Dict[str, List[str]]): Classified URLs by category

        Returns:
            Dict[str, str]: Representative URL for each category (using db field names as keys)
        """
        representative_urls = {}

        self.logger.info(
            "🎯 Starting representative URL selection with priority order implementation",
            {
                "total_categories": len(classified_urls),
                "categories_with_urls": len([c for c, urls in classified_urls.items() if urls])
            }
        )

        # Priority order: ONLY policy and social media categories (NO home_page, products, catalogue)
        priority_order = [
            "terms_and_condition", "privacy_policy",
            "shipping_delivery", "contact_us", "returns_cancellation_exchange", "about_us"
        ]
        
        # Process categories in priority order first (ONLY target categories)
        for category in priority_order:
            if category in classified_urls and classified_urls[category]:
                # Skip excluded categories (home_page, products, catalogue)
                if category in {"home_page", "products", "catalogue"}:
                    self.logger.info(f"🚫 Skipping excluded category: {category}", {
                        "category": category,
                        "reason": "not_policy_target",
                        "urls_count": len(classified_urls[category])
                    })
                    continue

                urls = classified_urls[category]

                # Map classification category to database field name
                db_category = category
                if category in self.social_media_categories:
                    db_category = self.social_media_categories[category]
                elif category in self.required_categories:
                    db_category = category

                # Select one URL from each category to avoid losing critical policy pages
                representative_urls[db_category] = urls[0]
                self.logger.info(
                    f"✅ Priority selection: {category} -> {db_category}",
                    {
                        "selected_url": urls[0],
                        "total_urls_in_category": len(urls),
                        "urls_skipped": len(urls) - 1,
                        "priority_rank": priority_order.index(category) + 1
                    }
                )

        # Then process any remaining social media categories with correct mapping
        social_media_classification_categories = ["instagram_page", "facebook_page", "youtube_page", 
                                                 "linkedin_page", "twitter_page", "pinterest_page", "x_page"]
        
        for classification_category in social_media_classification_categories:
            if classification_category in classified_urls and classified_urls[classification_category]:
                urls = classified_urls[classification_category]
                # Map from classification category to database field name
                db_category = self.social_media_categories.get(classification_category, classification_category.replace("_page", ""))
                
                if db_category not in representative_urls:  # Don't overwrite existing
                    representative_urls[db_category] = urls[0]
                    self.logger.info(
                        f"✅ Social media selection: {classification_category} -> {db_category}",
                        {
                            "selected_url": urls[0],
                            "total_urls_in_category": len(urls),
                            "urls_skipped": len(urls) - 1,
                            "category_type": "social_media",
                            "classification_category": classification_category,
                            "db_category": db_category
                        }
                    )

        self.logger.info(
            "🏁 Representative URL selection completed",
            {
                "selected_categories": len(representative_urls),
                "categories": list(representative_urls.keys()),
                "total_urls_selected": len(representative_urls)
            }
        )

        return representative_urls

    async def process_content_extraction(self, representative_urls: Dict[str, str]) -> Dict[str, Dict[str, str]]:
        """
        Process content extraction for all representative URLs with deduplication

        Args:
            representative_urls (Dict[str, str]): Representative URLs by category

        Returns:
            Dict[str, Dict[str, str]]: Content data by category
        """
        content_data = {}

        self.logger.info(
            "🔄 Starting content extraction for all categories",
            {
                "total_categories": len(representative_urls),
                "categories": list(representative_urls.keys())
            }
        )

        # Extract content for each URL
        for category, url in representative_urls.items():
            try:
                self.logger.info(f"Extracting content for {category}", {"url": url})
                
                text_content, screenshot_url = await self.extract_content_for_url(url, category)
                
                content_data[category] = {
                    "url": url,
                    "text": text_content,
                    "screenshot": screenshot_url
                }
                
                self.logger.info(f"Content extraction completed for {category}", {
                    "url": url,
                    "text_length": len(text_content) if text_content != "text_extraction_failed" else 0,
                    "has_screenshot": screenshot_url != "screenshot_failed"
                })
                
            except Exception as e:
                self.logger.error(f"Error extracting content for {category}", error=e)
                content_data[category] = {
                    "url": url,
                    "text": "text_extraction_failed",
                    "screenshot": "screenshot_failed"
                }

        # Fill in missing categories with "insufficient data"
        for category in self.all_db_categories:
            if category not in content_data:
                content_data[category] = {
                    "url": "not_found",
                    "text": "not_applicable",
                    "screenshot": "not_applicable"
                }

        self.logger.info(
            "✅ Content extraction completed for all categories",
            {
                "categories_processed": len(content_data),
                "categories_with_content": len([c for c, data in content_data.items() if data["url"] != "not_found"])
            }
        )

        return content_data

    def classify_urls(self, website, depth_1_urls, depth_2_urls):
        """
        Classify URLs using the URL classification service
        
        Args:
            website (str): The website URL
            depth_1_urls (list): URLs at depth 1
            depth_2_urls (list): URLs at depth 2
            
        Returns:
            tuple: (output_df, soft_classified) - DataFrame and dictionary of classified URLs
        """
        try:
            self.logger.info("Starting URL classification using proper service methods")

            # Create URL classification service INSTANCE with proper parameters
            service = urlclassification_service(
                website=website,
                scrape_request_ref_id=self.scrape_request_ref_id,
                org_id=self.org_id
            )

            # Call soft_classify_urls with the correct parameter order
            output_df, soft_classified = service.soft_classify_urls(
                depth_1_urls,  # urls1
                depth_2_urls,  # urls2
                self.logger,   # logger
                self.scrape_request_ref_id,  # scrape_request_ref_id
                self.org_id    # org_id
            )

            return output_df, soft_classified
        except Exception as e:
            self.logger.error("Error in URL classification", {"error": str(e)})
            self.logger.error(f"❌ ERROR: URL classification failed")
            raise

    def calculate_reachability_percentage(self, classified_urls: Dict[str, List[str]]) -> float:
        """
        Calculate reachability percentage based on classified URLs
        
        Args:
            classified_urls: Dictionary of classified URLs by category
            
        Returns:
            float: Reachability percentage
        """
        try:
            # Priority categories for analysis (ONLY policy categories - NO home_page, products, catalogue)
            priority_categories = {
                "terms_and_condition", "returns_cancellation_exchange",
                "privacy_policy", "shipping_delivery", "contact_us", "about_us"
            }
            
            # Get URLs that are not reachable from both categories
            urls_not_reachable = classified_urls.get("urls_not_reachable", [])
            unreachable_via_tool = classified_urls.get("Unreachable_via_tool", [])
            all_unreachable_urls = set(urls_not_reachable + unreachable_via_tool)
            
            # Count priority URLs in different states
            total_priority_urls = 0
            priority_urls_not_reachable = 0
            priority_urls_reachable = 0
            
            # Count total priority URLs and those that are not reachable
            for category in priority_categories:
                category_urls = classified_urls.get(category, [])
                total_priority_urls += len(category_urls)
                
                # Count how many priority URLs are in the combined unreachable set
                for url in category_urls:
                    if url in all_unreachable_urls:
                        priority_urls_not_reachable += 1
                    else:
                        priority_urls_reachable += 1
            
            # Calculate reachability percentage for priority URLs
            if total_priority_urls > 0:
                reachability_percentage = (priority_urls_reachable / total_priority_urls) * 100
            else:
                reachability_percentage = 0
                
            self.logger.info("Reachability calculation completed", {
                "total_priority_urls": total_priority_urls,
                "priority_urls_reachable": priority_urls_reachable,
                "priority_urls_not_reachable": priority_urls_not_reachable,
                "reachability_percentage": round(reachability_percentage, 2)
            })
            
            return reachability_percentage
            
        except Exception as e:
            self.logger.error("Error calculating reachability percentage", error=e)
            return 0.0

    def is_backup_flow_needed(self, classified_urls: Dict[str, List[str]]) -> bool:
        """
        CORRECTED FLOW LOGIC:
        
        CASE 1: Normal Flow (Primary Path)
        - Use when Unreachable_via_tool is NULL/EMPTY (all URLs are reachable)
        - Use hard classification output for policy URLs
        - Use soft classification output for social media URLs
        
        CASE 2: Backup Flow (Fallback Path)  
        - Use when Unreachable_via_tool is NOT NULL/EMPTY (some URLs are unreachable)
        - Use soft classification output for all URLs
        - Send both text AND images to Gemini for verification

        Args:
            classified_urls (Dict[str, List[str]]): Result from hard classification

        Returns:
            bool: True if backup flow is needed
        """
        try:
            # CORRECTED LOGIC: Check if Unreachable_via_tool is NOT NULL/EMPTY
            unreachable_via_tool = classified_urls.get("Unreachable_via_tool", [])
            
            if unreachable_via_tool and len(unreachable_via_tool) > 0:
                # URLs are unreachable → Use BACKUP FLOW (soft classification)
                self.logger.info(
                    "🚨 BACKUP FLOW TRIGGERED: URLs found in Unreachable_via_tool",
                    {
                        "backup_needed": True,
                        "trigger_reason": "UNREACHABLE_VIA_TOOL_NOT_EMPTY",
                        "unreachable_via_tool_count": len(unreachable_via_tool),
                        "unreachable_urls": unreachable_via_tool[:3],  # Show first 3 for debugging
                        "flow_decision": "BACKUP_FLOW",
                        "note": "Will use soft classification + Gemini image analysis"
                    }
                )
                return True
            else:
                # All URLs are reachable → Use NORMAL FLOW (hard classification)
                self.logger.info(
                    "✅ NORMAL FLOW SELECTED: All URLs are reachable",
                    {
                        "backup_needed": False,
                        "trigger_reason": "UNREACHABLE_VIA_TOOL_IS_EMPTY",
                        "unreachable_via_tool_count": 0,
                        "flow_decision": "NORMAL_FLOW",
                        "note": "Will use hard classification for policy + soft classification for social media"
                    }
                )
                return False

        except Exception as e:
            self.logger.error("Error in flow decision analysis", error=e)
            # Default to backup flow if decision logic fails
            self.logger.warning("Defaulting to backup flow due to decision error")
            return True

    async def process_backup_flow(self, classified_urls: Dict[str, List[str]], website: str) -> Dict[str, Dict[str, str]]:
        """
        Process backup flow when hard classification fails using soft classification
        Priority order: Policy categories (RAC, TNC, PP, SD, CU) first, then social media

        Args:
            classified_urls (Dict[str, List[str]]): URLs classified by category (soft classification)
            website (str): Website domain for analysis

        Returns:
            Dict[str, Dict[str, str]]: Content data by category
        """
        try:
            self.logger.info("🎯 Starting backup flow processing - TARGET CATEGORIES ONLY", {
                "categories_found": len(classified_urls),
                "max_pages_to_process": 6,
                "priority_order": "policy_first_then_social_media"
            })

            content_data = {}
            pages_processed = 0
            max_pages = 6  # Limit to 6 pages maximum for backup flow

            # 1. Separate policy and social media categories from soft classification
            policy_categories = []
            social_media_categories_to_process = []
            
            for category, urls in classified_urls.items():
                if not urls or category in {"home_page", "products", "catalogue", "Unreachable_via_tool", "urls_not_reachable"}:
                    if category in {"home_page", "about_us", "products", "catalogue", "Unreachable_via_tool"} and urls:
                        self.logger.info(f"🚫 Skipping excluded category: {category}", {
                            "category": category,
                            "urls_count": len(urls),
                            "reason": "excluded_from_policy_analysis",
                            "note": "home_page, about_us, products, catalogue are not policy targets"
                        })
                    continue
                    
                # 🎯 ONLY PROCESS TARGET CATEGORIES
                # Check if this is a TARGET policy category or TARGET social media category
                is_target_policy = category in self.required_categories
                is_target_social = category in self.social_media_categories
                
                if is_target_policy:
                    policy_categories.append(category)
                    self.logger.info(f"✅ Target policy category: {category}")
                elif is_target_social:
                    social_media_categories_to_process.append(category)
                    self.logger.info(f"✅ Target social media category: {category}")
                else:
                    # 🚫 IGNORE non-target categories (products, services, etc.)
                    self.logger.info(
                        f"🚫 Ignoring non-target category: {category}",
                        {
                            "category": category,
                            "reason": "not_in_target_list",
                            "ignored_types": ["products", "services", "other_non_target"]
                        }
                    )

            # 2. Process policy categories first in priority order (RAC, TNC, PP, SD, CU)
            priority_policy_order = [
                "terms_and_condition",           # TNC
                "returns_cancellation_exchange", # RAC  
                "privacy_policy",                # PP
                "shipping_delivery",             # SD
                "contact_us",                    # CU
                "about_us"                       # AU
            ]
            
            # Sort policy categories by priority order
            policy_categories_ordered = [cat for cat in priority_policy_order if cat in policy_categories]
            remaining_policy_categories = [cat for cat in policy_categories if cat not in priority_policy_order]
            policy_categories_final = policy_categories_ordered + remaining_policy_categories
            
            self.logger.info(f"🔄 Processing policy URLs first in priority order: {policy_categories_final}")
            for category in policy_categories_final:
                if pages_processed >= max_pages:
                    break
                    
                urls = classified_urls[category]
                if not urls:
                    continue
                    
                # LIMIT: Select only the first URL from each category (1 URL per category)
                representative_url = urls[0]
                self.logger.info(
                    f"🔄 Processing backup flow for policy category {category}",
                    {
                        "selected_url": representative_url,
                        "total_urls_in_category": len(urls),
                        "pages_processed": pages_processed,
                        "processing_order": "policy_first"
                    }
                )

                try:
                    # Extract content for the representative URL
                    extracted_text, screenshot_url = await self.extract_content_for_url(
                        representative_url, category
                    )

                    # 🔍 BACKUP FLOW: Verify category using Gemini image+text analysis
                    verified_category = await self.verify_category_with_gemini_image_analysis(
                        extracted_text, screenshot_url, representative_url, category, website  # Pass website parameter
                    )

                    # Use verified category result
                    db_category = verified_category
                    if verified_category in self.social_media_categories:
                        db_category = self.social_media_categories[verified_category]
                    elif verified_category in self.required_categories:
                        db_category = verified_category

                    self.logger.info(f"🎯 Backup flow: Policy category verified {category} -> {verified_category} -> {db_category}", {
                        "original_category": category,
                        "verified_category": verified_category,
                        "db_category": db_category,
                        "verification_method": "gemini_image_text_analysis"
                    })

                    if db_category and db_category not in content_data:
                        content_data[db_category] = {
                            "url": representative_url,
                            "text": extracted_text,
                            "screenshot": screenshot_url
                        }
                        
                        pages_processed += 1
                        
                        self.logger.info(f"🎉 Backup flow: Successfully processed policy {category} -> {db_category}", {
                            "url": representative_url,
                            "text_length": len(extracted_text) if extracted_text else 0,
                            "has_screenshot": screenshot_url != "screenshot_failed",
                            "pages_processed": pages_processed
                        })

                except Exception as e:
                    self.logger.error(f"💥 Backup flow: Error processing policy URL", {
                        "url": representative_url,
                        "category": category,
                        "error": str(e)
                    })
                    continue

            # 3. 📱 Process TARGET SOCIAL MEDIA URLs
            self.logger.info(f"📱 Processing TARGET social media URLs: {social_media_categories_to_process}")
            for category in social_media_categories_to_process:
                urls = classified_urls.get(category, [])
                if not urls:
                    continue
                    
                # Select only the first URL from each category
                representative_url = urls[0]
                
                self.logger.info(f"🔗 Processing backup flow for social media {category}", {
                    "selected_url": representative_url,
                    "total_urls_in_category": len(urls),
                    "pages_processed": pages_processed,
                    "processing_order": "social_media_after_policy"
                })

                # Map to database field name
                db_category = self.social_media_categories.get(category, category)
                
                try:
                    # Extract content for social media URL (screenshot only)
                    extracted_text, screenshot_url = await self.extract_content_for_url(
                        representative_url, category
                    )

                    if db_category and db_category not in content_data:
                        content_data[db_category] = {
                            "url": representative_url,
                            "text": "Social media URL - no text extraction needed",
                            "screenshot": screenshot_url
                        }
                        
                        pages_processed += 1
                        
                        self.logger.info(f"🔗 Backup flow: Added social media URL {category} -> {db_category}", {
                            "url": representative_url,
                            "has_screenshot": screenshot_url != "screenshot_failed",
                            "pages_processed": pages_processed
                        })

                except Exception as e:
                    self.logger.error(f"💥 Backup flow: Error processing social media URL", {
                        "url": representative_url,
                        "category": category,
                        "error": str(e)
                    })
                    
                    # Still add the URL to content_data even if processing failed
                    if db_category and db_category not in content_data:
                        content_data[db_category] = {
                            "url": representative_url,
                            "text": "Social media URL - processing failed",
                            "screenshot": "screenshot_failed"
                        }
                        
                        self.logger.info(f"🔗 Backup flow: Added failed social media URL {category} -> {db_category}", {
                            "url": representative_url,
                            "has_screenshot": False,
                            "processing_status": "failed_but_added"
                        })

            # Fill in missing categories with "insufficient data"
            for category in self.all_db_categories:
                if category not in content_data:
                    content_data[category] = {
                        "url": "not_found",
                        "text": "not_applicable",
                        "screenshot": "not_applicable"
                    }

            self.logger.info("Backup flow processing completed", {
                "categories_processed": len([c for c, data in content_data.items() if data["url"] != "not_found"]),
                "total_categories": len(content_data),
                "pages_processed": pages_processed,
                "categories": list(content_data.keys())
            })

            return content_data

        except Exception as e:
            self.logger.error(f"Error in backup flow: {str(e)}")
            return {}

    async def save_to_database(self, website: str, content_data: Dict[str, Dict[str, str]],
                              analysis_flow: str, reachability_percentage: float) -> bool:
        """
        Save analysis results to database

        Args:
            website (str): Website URL
            content_data (Dict[str, Dict[str, str]]): Content data by category
            analysis_flow (str): Flow used ("normal" or "backup")
            reachability_percentage (float): Reachability percentage

        Returns:
            bool: True if saved successfully
        """
        try:
            with Session(engine) as session:
                # Find existing record
                statement = select(PolicyAnalysisNew).where(
                    PolicyAnalysisNew.scrape_request_ref_id == self.scrape_request_ref_id
                )
                analysis = session.exec(statement).first()

                if not analysis:
                    self.logger.error("PolicyAnalysisNew record not found for update")
                    return False

                # Update analysis with content data
                for db_category, data in content_data.items():
                    url_field = f"{db_category}_url"
                    text_field = f"{db_category}_text"
                    screenshot_field = f"{db_category}_screenshot"

                    if hasattr(analysis, url_field):
                        setattr(analysis, url_field, data["url"])
                    if hasattr(analysis, text_field):
                        setattr(analysis, text_field, data["text"])
                    if hasattr(analysis, screenshot_field):
                        setattr(analysis, screenshot_field, data["screenshot"])

                # Update metadata
                analysis.analysis_flow_used = analysis_flow
                analysis.reachability_percentage = reachability_percentage
                analysis.total_urls_processed = len(content_data)
                analysis.processing_status = "COMPLETED"
                analysis.completed_at = get_current_time()

                session.commit()

                self.logger.info("Analysis results saved to database", {
                    "categories_saved": len(content_data),
                    "analysis_flow": analysis_flow,
                    "reachability_percentage": reachability_percentage
                })

                return True

        except Exception as e:
            self.logger.error(f"Error saving to database: {str(e)}")
            return False

    def _truncate_text_to_words(self, text: str, max_words: int) -> str:
        """
        Truncate text to a maximum number of words for API compatibility

        Args:
            text (str): Original text to truncate
            max_words (int): Maximum number of words to keep

        Returns:
            str: Truncated text or original if within limit
        """
        if not text or text == "not_applicable" or text == "text_extraction_failed":
            return text

        words = text.split()
        if len(words) <= max_words:
            return text

        truncated = " ".join(words[:max_words])
        self.logger.info(f"Text truncated from {len(words)} to {max_words} words for API compatibility")
        return truncated

    async def send_webhook(self, website: str, content_data: Dict[str, Dict[str, str]]) -> bool:
        """
        Send webhook notification with analysis results - ENHANCED LOGGING

        Args:
            website (str): Website URL
            content_data (Dict[str, Dict[str, str]]): Content data by category

        Returns:
            bool: True if webhook sent successfully
        """
        try:
            self.logger.info("Starting webhook preparation", {
                "website": website,
                "content_data_keys": list(content_data.keys()) if content_data else [],
                "content_data_size": len(content_data) if content_data else 0,
                "content_data_details": {k: {"has_url": bool(v.get("url")), "has_text": bool(v.get("text")), "has_screenshot": bool(v.get("screenshot"))} for k, v in content_data.items()} if content_data else {}
            })
            # Create a mapping from database category names to their types (RAC, TNC, etc.)
            category_to_type_mapping = {
                "returns_cancellation_exchange": "RAC",
                "terms_and_condition": "TNC",
                "privacy_policy": "PP",
                "shipping_delivery": "SD",
                "contact_us": "CU",
                "about_us": "AU",
                "instagram": "IG",
                "facebook": "FB",
                "twitter": "TW",
                "linkedin": "LI",
                "youtube": "YT",
                "pinterest": "PT",
                "x": "X"
            }

            # Process content data with special handling for AU exclusion and TW/X merging
            processed_content = {}
            twitter_data = None
            x_data = None

            # First pass: collect Twitter and X data, exclude AU
            for db_category, data in content_data.items():
                if db_category == "about_us":
                    # Skip About Us category - do not include in webhook payload
                    self.logger.info("🚫 Excluding About Us (AU) category from webhook payload", {
                        "category": db_category,
                        "reason": "excluded_by_requirement"
                    })
                    continue
                elif db_category == "twitter":
                    twitter_data = data
                    self.logger.info("📱 Collected Twitter data for merging", {
                        "category": db_category,
                        "has_url": bool(data.get("url")),
                        "has_text": bool(data.get("text")),
                        "has_screenshot": bool(data.get("screenshot"))
                    })
                elif db_category == "x":
                    x_data = data
                    self.logger.info("📱 Collected X data for merging", {
                        "category": db_category,
                        "has_url": bool(data.get("url")),
                        "has_text": bool(data.get("text")),
                        "has_screenshot": bool(data.get("screenshot"))
                    })
                else:
                    # Keep all other categories as-is
                    processed_content[db_category] = data

            # Second pass: merge Twitter and X data into single X category
            if twitter_data or x_data:
                # Determine which data to use for the merged X category
                merged_x_data = None
                merge_source = ""

                if x_data and twitter_data:
                    # Both exist - prioritize X data but log the merge
                    merged_x_data = x_data
                    merge_source = "X (prioritized over Twitter)"
                    self.logger.info("🔄 Merging Twitter and X data - prioritizing X data", {
                        "twitter_url": twitter_data.get("url", "not_found"),
                        "x_url": x_data.get("url", "not_found"),
                        "selected_source": "X"
                    })
                elif x_data:
                    # Only X data exists
                    merged_x_data = x_data
                    merge_source = "X only"
                elif twitter_data:
                    # Only Twitter data exists - use it for X category
                    merged_x_data = twitter_data
                    merge_source = "Twitter converted to X"
                    self.logger.info("🔄 Converting Twitter data to X category", {
                        "twitter_url": twitter_data.get("url", "not_found")
                    })

                if merged_x_data:
                    processed_content["x"] = merged_x_data
                    self.logger.info("✅ Successfully merged Twitter/X data", {
                        "merge_source": merge_source,
                        "final_category": "X",
                        "url": merged_x_data.get("url", "not_found")
                    })

            # Build policies array from processed content
            policies = []
            for db_category, data in processed_content.items():
                policy_type = category_to_type_mapping.get(db_category, db_category.upper())

                # Truncate text to maximum 100 words for API compatibility
                original_text = data["text"]
                truncated_text = self._truncate_text_to_words(original_text, 100)

                policies.append({
                    "type": policy_type,
                    "url": data["url"],
                    "imglink": data["screenshot"],
                    "text": truncated_text
                })

            # Log final policy types being sent
            policy_types_sent = [policy["type"] for policy in policies]
            self.logger.info("📤 Final webhook payload policy types", {
                "policy_types": policy_types_sent,
                "total_policies": len(policies),
                "excluded_categories": ["AU"],
                "merged_categories": "TW+X -> X" if (twitter_data or x_data) else "none"
            })

            # Build webhook payload
            payload = {
                "website": website,
                "scrapeRequestUuid": self.scrape_request_ref_id,
                "createdDate": datetime.now().isoformat() + "Z",
                "status": "COMPLETED",
                "policies": policies,
                "org_id": int(self.org_id) if self.org_id.isdigit() else 1
            }
            
            # Calculate payload size for logging
            payload_json = json.dumps(payload)
            payload_size = len(payload_json.encode('utf-8'))
            payload_size_kb = round(payload_size / 1024, 2)

            # ENHANCED: Log complete webhook request preparation
            self.logger.info("📤 WEBHOOK REQUEST PREPARATION - Enhanced Policy Service", {
                "webhook_url": self.webhook_url,
                "method": "PATCH",
                "headers": self.webhook_headers,
                "api_key_configured": bool(self.webhook_headers.get("X-API-KEY")),
                "api_key_length": len(self.webhook_headers.get("X-API-KEY", "")),
                "content_type": self.webhook_headers.get("Content-Type"),
                "payload_size_bytes": payload_size,
                "payload_size_kb": payload_size_kb,
                "policies_count": len(policies),
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "website": website,
                "org_id": payload["org_id"],
                "timestamp": datetime.now().isoformat()
            })

            # ENHANCED: Log complete payload structure
            self.logger.info("📋 WEBHOOK PAYLOAD STRUCTURE - Enhanced Policy Service", {
                "payload_structure": {
                    "website": payload["website"],
                    "scrapeRequestUuid": payload["scrapeRequestUuid"],
                    "createdDate": payload["createdDate"],
                    "status": payload["status"],
                    "org_id": payload["org_id"],
                    "policies_count": len(payload["policies"]),
                    "policy_types": [p["type"] for p in payload["policies"]],
                    "policy_details": [
                        {
                            "type": p["type"],
                            "has_url": bool(p["url"]) and p["url"] not in ["not_found", "text_extraction_failed"],
                            "has_screenshot": bool(p["imglink"]) and p["imglink"] not in ["screenshot_failed", "not_applicable"],
                            "has_text": bool(p["text"]) and p["text"] not in ["text_extraction_failed", "not_applicable"],
                            "url_preview": p["url"][:50] + "..." if len(p["url"]) > 50 else p["url"],
                            "text_length": len(p["text"]) if p["text"] else 0
                        }
                        for p in payload["policies"]
                    ]
                },
                "full_payload": payload  # Complete payload for debugging
            })

            # Generate curl command equivalent for debugging
            headers_str = " ".join([f"-H '{k}: {v}'" for k, v in self.webhook_headers.items()])
            payload_json_escaped = json.dumps(payload).replace("'", "\\'")
            curl_command = f"curl -X PATCH '{self.webhook_url}' {headers_str} -d '{payload_json_escaped}'"

            # ENHANCED: Log pre-send request details with curl equivalent
            self.logger.info("🚀 WEBHOOK REQUEST SENDING - Enhanced Policy Service", {
                "url": self.webhook_url,
                "method": "PATCH",
                "timeout": 30,
                "request_headers": self.webhook_headers,
                "payload_size_kb": payload_size_kb,
                "curl_equivalent": curl_command,
                "timestamp": datetime.now().isoformat()
            })

            # Send webhook
            response = requests.patch(
                self.webhook_url,
                json=payload,
                headers=self.webhook_headers,
                timeout=30
            )

            # ENHANCED: Log comprehensive response details
            response_size = len(response.content) if response.content else 0
            response_size_kb = round(response_size / 1024, 2) if response_size > 0 else 0

            self.logger.info("📥 WEBHOOK RESPONSE RECEIVED - Enhanced Policy Service", {
                "status_code": response.status_code,
                "success": response.status_code in [200, 201, 202],
                "response_text": response.text,
                "response_headers": dict(response.headers),
                "response_size_bytes": response_size,
                "response_size_kb": response_size_kb,
                "url": self.webhook_url,
                "payload_size_kb": payload_size_kb,
                "request_duration_ms": round(response.elapsed.total_seconds() * 1000, 2),
                "timestamp": datetime.now().isoformat()
            })

            if response.status_code in [200, 201, 202]:
                self.logger.info("✅ WEBHOOK SUCCESS - Enhanced Policy Service", {
                    "status_code": response.status_code,
                    "response": response.text[:200],
                    "policies_sent": len(policies),
                    "scrape_request_ref_id": self.scrape_request_ref_id
                })
                return True
            elif response.status_code == 401:
                self.logger.error("❌ WEBHOOK AUTHENTICATION ERROR - Enhanced Policy Service", {
                    "status_code": response.status_code,
                    "response": response.text,
                    "response_headers": dict(response.headers),
                    "curl_command": curl_command,
                    "error": "Invalid API key - check BIZTEL_API_KEY in settings",
                    "api_key_configured": bool(self.webhook_headers.get("X-API-KEY")),
                    "api_key_length": len(self.webhook_headers.get("X-API-KEY", "")),
                    "webhook_url": self.webhook_url
                })
                return False
            elif response.status_code == 500:
                self.logger.error("❌ WEBHOOK SERVER ERROR - Enhanced Policy Service", {
                    "status_code": response.status_code,
                    "response": response.text,
                    "response_headers": dict(response.headers),
                    "curl_command": curl_command,
                    "error": "Server error (possibly payload too large)",
                    "payload_size_kb": payload_size_kb,
                    "webhook_url": self.webhook_url
                })
                return False
            else:
                self.logger.error("❌ WEBHOOK FAILED - Enhanced Policy Service", {
                    "status_code": response.status_code,
                    "response": response.text,
                    "response_headers": dict(response.headers),
                    "curl_command": curl_command,
                    "webhook_url": self.webhook_url,
                    "payload_size_kb": payload_size_kb
                })
                return False

        except Exception as e:
            self.logger.error("❌ WEBHOOK EXCEPTION - Enhanced Policy Service", {
                "error": str(e),
                "error_type": type(e).__name__,
                "webhook_url": self.webhook_url,
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "traceback": traceback.format_exc()
            })
            return False

    async def process_policy_analysis(self) -> Dict[str, any]:
        """
        Main method to process policy analysis with unified soft → hard classification approach

        Returns:
            Dict[str, any]: Analysis result with status and details
        """
        start_time = time.time()

        try:
            self.logger.info("=" * 80)
            self.logger.info("🚀 ENHANCED POLICY ANALYSIS STARTED (UNIFIED APPROACH)")
            self.logger.info("=" * 80)
            self.logger.info(f"📋 Request ID: {self.scrape_request_ref_id}")
            self.logger.info(f"🏢 Organization: {self.org_id}")
            self.logger.info("=" * 80)

            # Step 1: Get website URL and URLs from database
            self.logger.info("")
            self.logger.info("📋 STEP 1: RETRIEVING URLs FROM DATABASE")
            self.logger.info("-" * 50)
            
            website = ""
            depth_1_urls = []
            depth_2_urls = []
            
            with Session(engine) as session:
                # Get website from PolicyAnalysisNew table
                statement = select(PolicyAnalysisNew).where(
                    PolicyAnalysisNew.scrape_request_ref_id == self.scrape_request_ref_id
                )
                analysis = session.exec(statement).first()
                if analysis:
                    website = analysis.website

                # Get URLs from WebsiteUrls table
                statement = select(WebsiteUrls).where(
                    WebsiteUrls.scrape_request_ref_id == self.scrape_request_ref_id
                )
                url_records = session.exec(statement).all()
                
                for record in url_records:
                    if record.depth == 1:
                        depth_1_urls.append(record.url)
                    elif record.depth == 2:
                        depth_2_urls.append(record.url)

            if not website:
                self.logger.error("❌ ERROR: Website not found in database")
                return {
                    "status": "FAILED",
                    "error": "Website not found",
                    "scrape_request_ref_id": self.scrape_request_ref_id
                }

            if not depth_1_urls and not depth_2_urls:
                self.logger.error("❌ ERROR: No URLs found in database")
                return {
                    "status": "FAILED",
                    "error": "No URLs found",
                    "scrape_request_ref_id": self.scrape_request_ref_id
                }

            total_urls = len(depth_1_urls) + len(depth_2_urls)
            self.logger.info("✅ RESULT: URLs successfully retrieved")
            self.logger.info(f"   📊 Website: {website}")
            self.logger.info(f"   📊 Depth 1 URLs: {len(depth_1_urls)}")
            self.logger.info(f"   📊 Depth 2 URLs: {len(depth_2_urls)}")
            self.logger.info(f"   📊 Total URLs: {total_urls}")

            # Step 2: Classify URLs using unified soft → hard approach
            self.logger.info("")
            self.logger.info("🏷️ STEP 2: CLASSIFYING URLs (UNIFIED SOFT → HARD)")
            self.logger.info("-" * 50)
            output_df, soft_classified = self.classify_urls(website, depth_1_urls, depth_2_urls)
            if output_df is None or output_df.empty or not soft_classified:
                self.logger.error("❌ ERROR: URL classification failed")
                return {
                    "status": "FAILED",
                    "error": "URL classification failed",
                    "scrape_request_ref_id": self.scrape_request_ref_id
                }

            self.logger.info("✅ RESULT: URL classification completed")
            self.logger.info(f"   📊 Categories found: {len(soft_classified)}")
            self.logger.info(f"   📋 Categories: {', '.join(soft_classified.keys())}")

            # Step 3: Determine flow based on classification results
            self.logger.info("\n🔀 STEP 3: DETERMINING PROCESSING FLOW\n--------------------------------------------------")
            
            # Decide if backup flow is needed based on classification results
            backup_needed = self.is_backup_flow_needed(soft_classified)
            reachability_percentage = self.calculate_reachability_percentage(soft_classified)

            content_data = {}
            analysis_flow = "backup" if backup_needed else "normal"

            if backup_needed:
                # -----------------
                #  BACKUP FLOW
                # -----------------
                self.logger.info("🔄 DECISION: Using BACKUP FLOW", {
                    "reason": "Unreachable_via_tool detected",
                    "trigger": "IMMEDIATE_FALLBACK_UNREACHABLE_VIA_TOOL",
                    "method": "Gemini text classification",
                    "reachability": f"{reachability_percentage:.2f}%"
                })
                
                # In backup flow, we use the SOFT classification results
                # Add social media pages from soft classification to the mix
                final_classified_urls = soft_classified.copy()
                social_categories_added = []
                
                for category, db_field in self.social_media_categories.items():
                    if category in soft_classified and soft_classified[category]:
                        final_classified_urls[category] = soft_classified[category]
                        social_categories_added.append(category)

                if social_categories_added:
                    self.logger.info("🔗 Social media URLs merged for backup flow", {
                        "social_categories_added": social_categories_added,
                        "total_categories_for_backup": len(final_classified_urls)
                    })

                content_data = await self.process_backup_flow(final_classified_urls, website)
            else:
                # -----------------
                #  NORMAL FLOW
                # -----------------
                self.logger.info("✅ DECISION: Using NORMAL FLOW (HARD CLASSIFICATION)", {
                    "reason": "All URLs verified as reachable",
                    "method": "Hard classification for policy, soft for social media",
                    "reachability": f"{reachability_percentage:.2f}%"
                })

                # FIXED: Use hard classification results directly - social media merging is now handled in URL classification service
                # The URL classification service now properly excludes social media from hard classification
                # and merges them back from soft classification automatically
                self.logger.info("✅ Using hard classification results (social media already merged by URL classification service)")

                content_data = await self.process_hard_classification_flow(soft_classified)

            # Final check if content_data is empty
            if not content_data:
                self.logger.error("❌ ERROR: No content data extracted")
                return {
                    "status": "FAILED",
                    "error": "No content extracted",
                    "scrape_request_ref_id": self.scrape_request_ref_id,
                    "analysis_flow": analysis_flow,
                    "reachability_percentage": reachability_percentage
                }

            # Step 4: Save to database
            self.logger.info("")
            self.logger.info("💾 STEP 4: SAVING ANALYSIS RESULTS")
            self.logger.info("-" * 40)
            save_success = await self.save_to_database(
                website, content_data, analysis_flow, reachability_percentage
            )

            if not save_success:
                self.logger.error("❌ ERROR: Failed to save analysis results to database")
                return {
                    "status": "FAILED",
                    "error": "Database save failed",
                    "scrape_request_ref_id": self.scrape_request_ref_id
                }

            self.logger.info("✅ RESULT: Analysis results saved successfully")

            # Step 5: Send webhook notification
            self.logger.info("")
            self.logger.info("📤 STEP 5: SENDING WEBHOOK NOTIFICATION")
            self.logger.info("-" * 40)
            webhook_success = await self.send_webhook(website, content_data)

            processing_time = time.time() - start_time

            # Log summary of what was captured
            successful_text = sum(1 for data in content_data.values() if data["text"] not in ["text_extraction_failed", "not_applicable"])
            successful_screenshots = sum(1 for data in content_data.values() if data["screenshot"] not in ["screenshot_failed", "not_applicable"])
            categories_with_urls = sum(1 for data in content_data.values() if data["url"] != "not_found")

            self.logger.info("✅ RESULT: Webhook notification sent")
            self.logger.info("")
            self.logger.info("📊 EXTRACTION SUMMARY:")
            self.logger.info(f"   📋 Total categories: {len(content_data)}")
            self.logger.info(f"   📝 Successful text extractions: {successful_text}")
            self.logger.info(f"   📸 Successful screenshots: {successful_screenshots}")
            self.logger.info(f"   🔗 Categories with URLs: {categories_with_urls}")
            self.logger.info(f"   ⏱️ Processing time: {processing_time:.2f}s")
            self.logger.info("")
            self.logger.info("🎉 ENHANCED POLICY ANALYSIS COMPLETED SUCCESSFULLY!")
            self.logger.info("=" * 80)

            result = {
                "status": "COMPLETED",
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "website": website,
                "analysis_flow": analysis_flow,
                "reachability_percentage": reachability_percentage,
                "categories_processed": len(content_data),
                "processing_time": processing_time,
                "webhook_sent": webhook_success,
                "unified_classification": True
            }

            self.logger.info("Enhanced policy analysis completed successfully", result)
            return result

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Error in enhanced policy analysis: {str(e)}"
            self.logger.error("❌ CRITICAL ERROR: Enhanced policy analysis failed", {
                "error": error_msg,
                "processing_time": processing_time,
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "traceback": traceback.format_exc()
            })

            return {
                "status": "FAILED",
                "error": error_msg,
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "processing_time": processing_time
            }
