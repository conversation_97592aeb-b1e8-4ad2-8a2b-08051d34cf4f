"""
Central module for Celery app instance to avoid circular imports
"""
from celery import Celery
from app.config import settings

# Initialize Celery
celery_app = Celery(
    "analysis_tasks",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND
)

# Configure Celery
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=1800,  # 30 minutes timeout
    worker_max_tasks_per_child=200,  # Restart worker after 200 tasks
    broker_connection_retry_on_startup=True,
    include=['app.tasks.test_task', 'app.tasks.celery_tasks'],

    # Queue routing for policy analysis tasks
    task_routes={
        # Policy Analysis - I/O + CPU intensive, dedicated queue
        'process_policy_analysis': {'queue': 'policy_queue'},
        'process_policy_analysis_enhanced': {'queue': 'policy_queue'},

        # General tasks
        'test_task': {'queue': 'general_queue'},
    },

    # Worker configuration optimized for 4 vCPU instance
    worker_prefetch_multiplier=1,  # Prevent worker hoarding tasks
    task_acks_late=True,  # Acknowledge tasks after completion
    worker_disable_rate_limits=False,  # Keep rate limits for API protection
)

# Include task modules
celery_app.autodiscover_tasks(['app.tasks'], force=True)

# Import tasks to ensure they're registered
import app.tasks 