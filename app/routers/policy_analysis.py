"""
Enhanced Policy Analysis API Router - Using new screenshot service with conditional popup handling
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select
import logging

from app.database import get_session
from app.models.db_models import PolicyAnalysisNew, get_current_time
from app.models.request_models import PolicyAnalysisRequest, PolicyAnalysisPatchRequest
from app.utils.website_url_processor import store_urls_from_request
from app.tasks.celery_tasks import process_policy_analysis_enhanced

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/", tags=["Policy Analysis"], summary="Run enhanced policy analysis")
async def run_enhanced_policy_analysis(
    request_data: PolicyAnalysisRequest,
    session: Session = Depends(get_session),
):
    """
    Run enhanced policy analysis using the PolicyAnalysisEnhancedService.
    This endpoint processes URLs to extract policy and social media information.
    """
    try:
        # Step 1: Store URLs from request into database
        logger.info(f"Storing URLs for policy analysis: {request_data.scrapeRequestRefID}")
        url_store_result = store_urls_from_request(request_data, session)
        if url_store_result != 1:  # 1 indicates success, -1 indicates failure
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to store website URLs"
            )
        
        # Step 2: Create PolicyAnalysisNew record
        analysis_record = PolicyAnalysisNew(
            website=request_data.website,
            scrape_request_ref_id=request_data.scrapeRequestRefID,
            org_id=str(request_data.org_id),
            processing_status="PENDING",
            created_at=get_current_time()
        )
        session.add(analysis_record)
        session.commit()
        session.refresh(analysis_record)
        
        # Step 3: Queue the Celery task (this will run in worker and log to worker.log)
        logger.info(f"Queuing enhanced policy analysis task for analysis_id: {analysis_record.id}")
        task = process_policy_analysis_enhanced.delay(analysis_record.id)
        
        return {
            "status": "success",
            "message": "Enhanced policy analysis has been queued.",
            "scrape_request_ref_id": request_data.scrapeRequestRefID,
            "task_id": task.id,
            "analysis_id": analysis_record.id
        }
    except Exception as e:
        logger.error(f"Error queuing policy analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/{scrape_request_ref_id}")
async def get_policy_analysis_status(scrape_request_ref_id: str, session: Session = Depends(get_session)):
    """
    Get the status of a policy analysis request
    
    Args:
        scrape_request_ref_id (str): The scrape request reference ID
        
    Returns:
        dict: Status information for the analysis
    """
    try:
        logger.info(f"Status check requested for {scrape_request_ref_id}")
        
        # Get analysis record
        statement = select(PolicyAnalysisNew).where(
            PolicyAnalysisNew.scrape_request_ref_id == scrape_request_ref_id
        )
        analysis = session.exec(statement).first()
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )
        
        # Build status response
        status_info = {
            "scrape_request_ref_id": scrape_request_ref_id,
            "website": analysis.website,
            "status": analysis.processing_status,
            "analysis_type": "enhanced_policy_analysis",
            "created_at": analysis.created_at,
            "started_at": analysis.started_at,
            "completed_at": analysis.completed_at,
            "org_id": analysis.org_id
        }
        
        # Add additional info if completed
        if analysis.processing_status == "COMPLETED":
            status_info.update({
                "analysis_flow_used": getattr(analysis, "analysis_flow_used", None),
                "reachability_percentage": getattr(analysis, "reachability_percentage", None),
                "total_urls_processed": getattr(analysis, "total_urls_processed", None)
            })
        
        logger.info(f"Status returned for {scrape_request_ref_id}: {analysis.processing_status}")
        return status_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting status for {scrape_request_ref_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving analysis status"
        )


@router.patch("/update")
async def update_policy_analysis(request: PolicyAnalysisPatchRequest, session: Session = Depends(get_session)):
    """
    PATCH endpoint for updating policy analysis results (for external integrations)
    
    Args:
        request (PolicyAnalysisPatchRequest): Update request with policy data
        
    Returns:
        dict: Update confirmation
    """
    try:
        logger.info(f"Policy analysis update received for {request.scrapeRequestUuid}")
        
        # Find analysis record
        statement = select(PolicyAnalysisNew).where(
            PolicyAnalysisNew.scrape_request_ref_id == request.scrapeRequestUuid
        )
        analysis = session.exec(statement).first()
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis record not found"
            )
        
        # Update analysis status
        analysis.processing_status = request.status
        analysis.completed_at = request.createdDate
        
        # Update policy data from request
        policy_type_mapping = {
            "RAC": "returns_cancellation_exchange",
            "TNC": "terms_and_condition",
            "PP": "privacy_policy",
            "SD": "shipping_delivery",
            "CU": "contact_us",
            "AU": "about_us",
            "HP": "home_page",
            "IG": "instagram",
            "FB": "facebook",
            "TW": "twitter",
            "LI": "linkedin",
            "YT": "youtube",
            "PT": "pinterest",
            "X": "x"
        }
        
        for policy in request.policies:
            db_category = policy_type_mapping.get(policy.type)
            if db_category:
                url_field = f"{db_category}_url"
                text_field = f"{db_category}_text"
                screenshot_field = f"{db_category}_screenshot"
                
                if hasattr(analysis, url_field):
                    setattr(analysis, url_field, policy.url)
                if hasattr(analysis, text_field):
                    setattr(analysis, text_field, policy.text)
                if hasattr(analysis, screenshot_field):
                    setattr(analysis, screenshot_field, policy.imglink)
        
        session.commit()
        
        logger.info(f"Policy analysis updated successfully for {request.scrapeRequestUuid}")
        
        return {
            "message": "Policy analysis updated successfully",
            "scrape_request_ref_id": request.scrapeRequestUuid,
            "status": request.status,
            "policies_updated": len(request.policies)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating policy analysis: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating policy analysis"
        )
