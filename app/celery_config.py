"""
Celery configuration and task registration
"""
from app.celery_app import celery_app

@celery_app.task(
    name='app.tasks.policy_task.policy_analysis_task',
    bind=True,
    time_limit=2700,  # 45 minutes hard timeout (increased from 30)
    soft_time_limit=2400,  # 40 minutes soft timeout (increased from 20)
    max_retries=3,  # Maximum number of retries
    retry_backoff=True,  # Exponential backoff for retries
    retry_backoff_max=900,  # Maximum backoff time (15 minutes, increased from 10)
    retry_jitter=True,  # Add random jitter to retry delay
)
def policy_analysis_task_wrapper(self, website_url, scrape_request_ref_id, parsed_urls_data, org_id):
    """
    Wrapper function for the policy analysis task
    
    Args:
        self: Task instance (added with bind=True)
        website_url: URL of the website to analyze
        scrape_request_ref_id: Reference ID for the scrape request
        parsed_urls_data: Data containing parsed URLs
        org_id: Organization ID
        
    Returns:
        Results from the policy analysis task
    """
    try:
        from app.tasks.celery_tasks import process_policy_analysis
        return process_policy_analysis(website_url, scrape_request_ref_id, parsed_urls_data, org_id)
    except Exception as exc:
        self.retry(exc=exc, countdown=120 * (2 ** (self.request.retries)))  # Exponential backoff

# Import the test task
