from typing import List, Optional, Union
from pydantic import BaseModel, HttpUrl, Field, field_validator, ConfigDict
import html


class UrlDepthItem(BaseModel):
    url_depth: int
    urls: List[str]  # Changed from HttpUrl to str to handle HTML-encoded URLs

    @field_validator('urls', mode='before')
    @classmethod
    def decode_html_entities(cls, v):
        """Decode HTML entities in URLs (e.g., &amp; to &)"""
        if isinstance(v, list):
            return [html.unescape(url) if isinstance(url, str) else url for url in v]
        return v


class PolicyAnalysisRequest(BaseModel):
    website: str = Field(..., description="URL of the website to analyze")
    scrapeRequestRefID: str = Field(..., description="Reference ID for the scrape request")
    parsed_urls: List[UrlDepthItem] = Field(..., description="Parsed URLs with depth information")
    org_id: Optional[Union[str, int]] = Field(default="default", description="Organization ID")

    @field_validator('org_id', mode='before')
    @classmethod
    def convert_org_id_to_string(cls, v):
        """Convert org_id to string if it's an integer"""
        if v is not None:
            return str(v)
        return "default"

    class Config:
        json_schema_extra = {
            "example": {
                "website": "example.com",
                "scrapeRequestRefID": "REQ12345",
                "parsed_urls": [
                    {
                        "url_depth": 1,
                        "urls": ["https://example.com/url1", "https://example.com/url2"],
                    },
                    {
                        "url_depth": 2,
                        "urls": ["https://example.com/url3", "https://example.com/url4"],
                    },
                ],
                "org_id": "1234567890",
            }
        }

class PolicyRequest(BaseModel):
    model_config = ConfigDict(populate_by_name=True)
    
    website: str
    scrapeRequestRefID: str
    parsed_urls: List[UrlDepthItem]
    registerName: Optional[str] = None
    org_id: Optional[Union[str, int]] = None
    @field_validator('org_id', mode='before')
    @classmethod
    def convert_to_string(cls, v):
        """Convert org_id to string if it's an integer, set default if None"""
        if v is None:
            return "default"
        return str(v)





class PolicyItem(BaseModel):
    """Individual policy item for PATCH request"""
    type: str = Field(..., description="Policy type (RAC, TNC, PP, SD, CU, AU, IG, FB, etc.)",
                     pattern="^(RAC|TNC|PP|SD|CU|AU|IG|FB|PT|YT|LI|TW|X)$")
    url: str = Field(..., description="URL of the policy page", min_length=1)
    imglink: str = Field(..., description="Screenshot URL from Azure storage", min_length=1)
    text: str = Field(default="", description="Extracted text content from the URL")


class PolicyAnalysisPatchRequest(BaseModel):
    """PATCH request model for policy analysis updates"""
    website: str = Field(..., description="Website domain", min_length=1)
    scrapeRequestUuid: str = Field(..., description="Scrape request UUID", min_length=1)
    createdDate: str = Field(..., description="Creation date in ISO format",
                            pattern=r"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$")
    status: str = Field(..., description="Analysis status (COMPLETED, PENDING, etc.)",
                       pattern="^(COMPLETED|PENDING|PROCESSING|FAILED)$")
    policies: List[PolicyItem] = Field(..., description="List of policy items", min_items=1)
    org_id: Union[str, int] = Field(..., description="Organization ID (string or integer)")

    @field_validator('org_id', mode='before')
    @classmethod
    def validate_org_id(cls, v):
        """Accept both string and integer org_id values"""
        if v is None:
            return "default"
        # Keep as-is for both string and integer values
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "website": "example.com",
                "scrapeRequestUuid": "uuid-123-456",
                "createdDate": "2024-01-01T12:00:00.000Z",
                "status": "COMPLETED",
                "policies": [
                    {
                        "type": "RAC",
                        "url": "https://example.com",
                        "imglink": "https://storage.url/screenshot1.jpg",
                        "text": "text extracted from the url"
                    },
                    {
                        "type": "TNC",
                        "url": "https://example.com/terms",
                        "imglink": "https://storage.url/screenshot2.jpg",
                        "text": "text extracted from the url"
                    },
                    {
                        "type": "PP",
                        "url": "https://example.com/privacy",
                        "imglink": "https://storage.url/screenshot3.jpg",
                        "text": "text extracted from the url"
                    },
                    {
                        "type": "AU",
                        "url": "https://example.com/about",
                        "imglink": "https://storage.url/screenshot4.jpg",
                        "text": "text extracted from the about us page"
                    }
                ],
                "org_id": 1
            }
        }

class SocialMediaRequest(BaseModel):
    website: str
    scrapeRequestRefID: str
    Instagram: str
    X: str
    Facebook: str
    Twitter: str
    LinkedIn: str
    TikTok: str
    YouTube: str
    org_id: str

    class Config:
        json_schema_extra = {
            "example": {
                "website": "example.com",
                "scrapeRequestRefID": "REQ12345",
                "Instagram": "https://example.com/url1",
                "X": "https://example.com/url2",
                "Facebook": "https://example.com/url3",
                "Twitter": "https://example.com/url4",
                "LinkedIn": "https://example.com/url5",
                "TikTok": "https://example.com/url6",
                "YouTube": "https://example.com/url7",
                "org_id": "1234567890",
            }
        }




