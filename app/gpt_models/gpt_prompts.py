import ast
import time
import json
import pandas as pd
from app.utils.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as logger
from app.gpt_models.chatgpt_utils import (
    construct_gpt_messages,
    num_tokens_from_string,
    clean_json_string,
)
from app.utils.logger import ConsoleLogger
from app.gpt_models.chatgpt_utils import <PERSON>TLogger as gpt_logger


class GptPromptPicker:
    @staticmethod
    def get_website_information_prompt(website, priority_urls):
        """Generate a prompt for extracting website information with error handling"""
        # try:
        #     # Validate inputs
        #     if not website or not isinstance(website, str):
        #         website = "Unknown website"
            
        #     if not priority_urls or not isinstance(priority_urls, dict):
        #         priority_urls = {"home_page": [website], "about_us": [website], "catalogue": [website], "products": [website]}
            
        #     # Create a formatted string of URLs by category
        #     url_sections = []
        #     for category, urls in priority_urls.items():
        #         try:
        #             if urls and isinstance(urls, list):
        #                 # Filter valid URLs and limit to 3 examples
        #                 valid_urls = [str(url) for url in urls if url and isinstance(url, str)][:3]
        #                 if valid_urls:
        #                     url_examples = "\n".join([f"   - {url}" for url in valid_urls])
        #                     url_sections.append(f"{str(category).upper()} URLs:\n{url_examples}")
        #         except Exception as url_error:
        #             # Skip this category if there's an error
        #             continue
            
        #     # Fallback if no valid URL sections found
        #     if not url_sections:
        #         url_sections = [f"HOME_PAGE URLs:\n   - {website}"]
            
        #     formatted_urls = "\n\n".join(url_sections)
            
        # except Exception as e:
        #     # Fallback formatting if anything fails
        #     formatted_urls = f"HOME_PAGE URLs:\n   - {website if website else 'Unknown website'}"
        priority_urls = {
            "home_page": [website], 
            "about_us": [website[0]] if website[0] else None, 
            "catalogue": [website[:5]] if website[:5] else None, 
            "products": [website[:5]] if website[:5] else None
        }

        print(priority_urls)
        
        prompt = f"""
            * Role * : You are an expert in analysing and extracting infromation from website. The information you extract will be used to classify this website into a appropiate merchant category code (referred as MCC). 

        * Task Starts *
        You will be given a website's URLs such home page, about us, catalogue page etc. Your task will be to visit these websites URLs and extract the information such as.  
        1. If the website is live and reachable
        2. If the website redirects to the other website
        3. Distinct types of products and services this website sells or offers. 
        4. Line of business of this website.
        5. Set of customers this website caters to.
        6. A short website description which can be very helpful for categorizing this busniess to a MCC. 
        * Task Ends *

        * Input Starts *
        You are given following things
        1. Website : {website}, product_services 
        priority_urls : {priority_urls}

        These URLs can sometime can be missing as well. You will have to decide accordingly.
        * Input Ends *

        * Guidelines *
        1. Firstly, verify the website URLs from which you have to extract information and strictly ensure that they are from the provided website only.
        2. Secondly, check if the wesbite is active and is valid. It should be NOT be that website is not reachable OR its domian is us for sale.
        3. Extract all the products, services, their description which are mentioned in the website to come up with the distinct types of products and services and line of business.
        4. Extract the line of business of this website by going through all the URLs provided to you.
        5. Look at the keywords in products and services first and then the images in the website for extracting the set of customers to which this website caters to, for e.g. men, women, boys, girls, children, infants, general public, pets such as dogs, cats etc. 
        6. Generally home page or catalogue will have all the categories of products. 

        * Things to keep in mind *
        1. You must extract information from the website only and only using the provided website URL do not include if it renavigates to another website or a new domain.
        2. Do not assume any information, if you are not sure about the information, do not include it in the output.
        3. Website's URL and its meaning may be misleading so tread carefully.
        4. Do not use your past knowledge regarding the website, extracted infroation should STRICLTY reflect realtime status of the website.
        5. while extracting the set of custemers refer explicit keywords first for example: men, women, boys, girls, children, infants, general public, pets such as dogs, cats etc and then refer images if the keywords arenot explecitely mentioned.
        6. for assigning kids and childrens in customers list it should be specifically mentioned in the website.

        Please note : Do not continue the analysis if the website is not valid or is redirecting to any other different domain name. Simly assign "is_valid_website" as "no" and "website_re_direction as "yes".

        * Output format starts *

        The output should be strictly be in a json format with the below 6 keys and do not return text before or after.
        {{
        "is_valid_website" : <str> "yes" or "no", only one amongst yes and no if the site is not active
        "website_redirection": <str> "yes" or "no"
        "product_services": <str>, holistic types covering product and services,
        "line_of_business": <str>, 
        "customers": <str>
        "website_description": <str>
        }}
        * Output format ends *
        """
        
        return prompt





    
    @staticmethod
    def _build_user_message_template(website, processed_urls):
        """Helper method to build the user message template with processed URLs"""
        return f"""
    * Information on website and its URLs starts *
    website name --> {website}
    dictionary of URLs of the website named "dictionary_of_URLs" --> {processed_urls}
    "dictionary_of_URLs" will contain following information
        a. key will be an integer
        b. value will be the URL corresponding to this integer key
    * Information on website and URLs ends *


    * Tasks Starts *
    You will have to categorize these website's URLs into the following categories.
    ['home_page','about_us','terms_and_condition','returns_cancellation_exchange','privacy_policy','shipping_delivery','contact_us','products','services','catalogue','instagram_page','facebook_page','youtube_page','twitter_page','linkedin_page','pinterest_page']
    You have to extract the index corresponding to these categories.
    You will have to output just index that correspond the correct URLs.


    * Guidelines for the task *
    1. Please go through all the key words mentioned in the URLs and think of a logical reason why it can be classified into these URLs.
    2. home page is generally the landing page and also the shortest URL amongst all the URL.
    3. about us page will have companies information and not founders or people information on it.
    4. Cataloge URL will be the one having all categories of products and services at one place. Assign catalogue URL only whan you are very very sure about it.
    5. Cataloge URL can be something like, "collections", "shop", "all products" , "all services" etc.
    6. Extract only 5 random indexes of product and service pages. These index should have maximum possible products and services. Try not to extract URLs which are very specific to just one product and service.
    7. URLs containing social media links for the business will mostly have respective name in the URL, like instagram, facebook, twitter, linkedin, youtube, pinterest
    8. LinkedIn page should be of the company/business. It should not be of the founder/people.
    9. Social media URLs should be of the company or the business. DO NOT output the founder page for facebook, youtube and instagram. 
    10. If you have slighest of chance that a URL can lie in any of these catgories, please assign it to that category.
    11. Do not output an index which is not there in any key.

    *** Important Note *** --> Please note that one same URL can go to 2 or more categories.

    * Tasks Ends *

    Output shoud strictly be in a json format containing these 16 keys.
            1. 'home_page':[], a list of integers which represent the URLs in "dictionary_of_URL", -->  Stick to one URL, multiple home page URLs are not required.
            2. 'about_us':[], a list of integers which represent the URLs in "dictionary_of_URL", -->  Stick to one URL, multiple home page URLs are not required.
            3. 'terms_and_condition':[], a list of integers which represent the URLs in "dictionary_of_URL"
            4. 'returns_cancellation_exchange':[], a list of integers which represent the URLs in "dictionary_of_URL"
            5. 'privacy_policy':[], a list of integers which represent the URLs in "dictionary_of_URL"
            6. 'shipping_delivery':[], a list of integers which represent the URLs in "dictionary_of_URL"
            7. 'contact_us':[], a list of integers which represent the URLs in "dictionary_of_URL"
            8. 'products':[], a list of integers with maximum 5 entries which represent the URLs in "dictionary_of_URL"
            9. 'services':[], a list of integers with maximum 5 entries which represent the URLs in "dictionary_of_URL"
            10.'catalogue':[], a list with just one integer which represent the URLs in "dictionary_of_URL"
            11.'instagram_page':[], a list with just one integer which represent the URLs in "dictionary_of_URL"
            12.'facebook_page':[], a list with just one integer which represent the URLs in "dictionary_of_URL"
            13.'twitter_page':[], a list with just one integer which represent the URLs in "dictionary_of_URL"
            14.'linkedin_page':[], a list with just one integer which represent the URLs in "dictionary_of_URL"
            15.'youtube_page':[], a list with just one integer which represent the URLs in "dictionary_of_URL"
            16.'pinterest_page':[], a list with just one integer which represent the URLs in "dictionary_of_URL"

            Output strictly should not contain any thing apart from is json.
            Strictly avoid any keywords or string outsisde this list.
    """

    @staticmethod
    def get_gpt_4o_soft_classification_prompt(website, dictOfListURLs):
        
        try:
            start_time = time.time()
            logger = ConsoleLogger(gpt_logger.analysis_id)

            logger.info(
                "Starting URL processing for model policy result",
                {
                    "website": website,
                    "total_urls": len(dictOfListURLs),
                },
            )

            # Increased token limit from 70K to 90K
            TOKEN_LIMIT = 90_000
            
            # Pre-calculate FULL system message token count
            full_system_message = """  **Context*
    You are an experienced Business Analayst who deals with the information around companies and its websites and therefore you are very well versed with the URLs.
    You have all the capability to just look at the URL and decide on the categry of the URL, if its a
        1. home page URL, OR
        2. about us page URLs OR
        3. contact us or customer support URLs OR
        4. terms and condition URLs OR
        5. privacy policy URLs OR
        6. return, refund and cancellation URLs OR
        7. products URLs
        8. service URLs
        9. delivery or shipping policy related URLs OR
        10. catalouge URLs
        11. instagram URL page of the business  
        12. facebook URL page of the business 
        13. youtube URL page of the business 
        14. twitter URL page of the business 
        15. linkedin URL page of the business 
        16. pinterest URL page of the business

    You will be given a website name and dictionary of URLs for that websites, dictionary will contain keys as an integer index and values as URL correspnding to the integer index.
    """

            # Calculate token count for full system message
            system_tokens = num_tokens_from_string(full_system_message, "cl100k_base")
            
            # Calculate base user message template (without URLs) using empty dict
            base_user_template = GptPromptPicker._build_user_message_template(website, {})
            base_user_tokens = num_tokens_from_string(base_user_template, "cl100k_base")
            
            # Calculate available tokens for URLs
            base_tokens = system_tokens + base_user_tokens
            available_tokens_for_urls = TOKEN_LIMIT - base_tokens - 1000  # Reserve 1000 tokens for safety
            
            logger.info(f"Token calculation: system={system_tokens}, base_user={base_user_tokens}, available_for_urls={available_tokens_for_urls}")
            
            # Prepare URLs to fit within token limit
            # First, calculate total tokens for all URLs to see if we need to trim
            total_url_tokens = 0
            sorted_indices = sorted(dictOfListURLs.keys())
            
            for url_index in sorted_indices:
                url = dictOfListURLs[url_index]
                url_tokens = num_tokens_from_string(str(url), "cl100k_base")
                total_url_tokens += url_tokens
            
            logger.info(f"Total URL tokens: {total_url_tokens}, Available: {available_tokens_for_urls}")
            
            if total_url_tokens <= available_tokens_for_urls:
                # All URLs fit, use all of them
                processed_urls = dictOfListURLs.copy()
                curr_urls_tokens = total_url_tokens
                logger.info("All URLs fit within token limit")
            else:
                # Need to trim URLs - use range-based approach
                logger.warning(f"URLs exceed token limit. Need to trim from {len(dictOfListURLs)} URLs")
                
                # Calculate how many URLs we can fit
                curr_urls_tokens = 0
                max_index = 0
                
                for url_index in sorted_indices:
                    url = dictOfListURLs[url_index]
                    url_tokens = num_tokens_from_string(str(url), "cl100k_base")
                    
                    if curr_urls_tokens + url_tokens < available_tokens_for_urls:
                        curr_urls_tokens += url_tokens
                        max_index = url_index
                    else:
                        break
                
                # Use your suggested method: create dictionary for range 0 to max_index
                processed_urls = {k: dictOfListURLs[k] for k in range(max_index + 1) if k in dictOfListURLs}
                
                logger.warning(f"Trimmed URLs from {len(dictOfListURLs)} to {len(processed_urls)} (keeping indices 0-{max_index})")
            
            # Calculate final total tokens
            final_total_tokens = base_tokens + curr_urls_tokens
            
            # Log final URL counts
            logger.info("Final URLs after token limiting", {
                "original_url_count": len(dictOfListURLs),
                "final_url_count": len(processed_urls),
                "urls_trimmed": len(dictOfListURLs) - len(processed_urls),
                "system_tokens": system_tokens,
                "base_user_tokens": base_user_tokens,
                "url_tokens": curr_urls_tokens,
                "final_total_tokens": final_total_tokens,
                "token_limit": TOKEN_LIMIT,
                "remaining_tokens": TOKEN_LIMIT - final_total_tokens
            })
            
            # Construct the system message and user message using helper method
            system_message = full_system_message
            user_message = GptPromptPicker._build_user_message_template(website, processed_urls)

            # Final verification: Calculate actual prompt tokens
            final_prompt = system_message + user_message
            actual_prompt_tokens = num_tokens_from_string(final_prompt, "cl100k_base")
            
            logger.info("Final prompt verification", {
                "actual_prompt_tokens": actual_prompt_tokens,
                "token_limit": TOKEN_LIMIT,
                "within_limit": actual_prompt_tokens < TOKEN_LIMIT,
                "processing_time": time.time() - start_time
            })
            
            if actual_prompt_tokens >= TOKEN_LIMIT:
                logger.error(f"CRITICAL: Final prompt exceeds token limit! {actual_prompt_tokens} >= {TOKEN_LIMIT}")
                # Emergency URL trimming - remove 20 URLs at a time
                while actual_prompt_tokens >= TOKEN_LIMIT and len(processed_urls) > 10:
                    # Remove 20 URLs at once or all remaining if less than 20
                    urls_to_remove = min(20, len(processed_urls) - 10)
                    original_count = len(processed_urls)
                    
                    # Get the highest keys to remove (remove from the end)
                    keys_to_remove = sorted(processed_urls.keys())[-urls_to_remove:]
                    for key in keys_to_remove:
                        del processed_urls[key]
                    
                    # Reconstruct user message with new URL dictionary using helper method
                    user_message = GptPromptPicker._build_user_message_template(website, processed_urls)
                    
                    # Recalculate with new prompt
                    final_prompt = system_message + user_message
                    actual_prompt_tokens = num_tokens_from_string(final_prompt, "cl100k_base")
                    logger.warning(f"Emergency trim: Removed {urls_to_remove} URLs (from {original_count} to {len(processed_urls)}), new token count: {actual_prompt_tokens}")
                    
                    # Safety break to avoid infinite loop
                    if urls_to_remove == 0:
                        logger.error("Cannot remove more URLs, breaking emergency trim loop")
                        break

            # Return the combined prompt for direct use with OpenAI API
            return final_prompt

        except Exception as e:
            logger = ConsoleLogger(gpt_logger.analysis_id)
            logger.error(f"Error in GPT request preparation: {str(e)}")
            return f"Error preparing prompt: {str(e)}"

    @staticmethod
    def gemini_25_flash_hard_classification_prompt(website, website_urls_dict):
        
        try:
            logger = ConsoleLogger(gpt_logger.analysis_id)
            
            # Hard classification URL limit
            MAX_URLS_HARD_CLASSIFICATION = 20
            
            logger.info("Hard classification input", {
                "total_urls": len(website_urls_dict),
                "website": website,
                "url_limit": MAX_URLS_HARD_CLASSIFICATION
            })
            
            # Limit URLs to maximum 20 for hard classification
            if len(website_urls_dict) > MAX_URLS_HARD_CLASSIFICATION:
                logger.warning("URLs exceed hard classification limit - trimming to maximum", {
                    "original_count": len(website_urls_dict),
                    "max_allowed": MAX_URLS_HARD_CLASSIFICATION,
                    "website": website
                })
                
                # Take first 20 URLs (keeping lowest indices for consistency)
                sorted_indices = sorted(website_urls_dict.keys())
                limited_indices = sorted_indices[:MAX_URLS_HARD_CLASSIFICATION]
                limited_urls_dict = {k: website_urls_dict[k] for k in limited_indices}
                
                logger.info("Hard classification URLs limited successfully", {
                    "kept_indices_range": f"{min(limited_indices)}-{max(limited_indices)}",
                    "final_count": len(limited_urls_dict)
                })
            else:
                limited_urls_dict = website_urls_dict.copy()
                logger.info("All URLs fit within hard classification limit", {
                    "total_urls": len(website_urls_dict),
                    "limit": MAX_URLS_HARD_CLASSIFICATION
                })
        
        except Exception as e:
            logger = ConsoleLogger(gpt_logger.analysis_id)
            logger.error("Error in hard classification URL limiting", error=e)
            # Fallback to original dict if error occurs
            limited_urls_dict = website_urls_dict.copy()
        
        system = f"""
        *Context*
    You are an expert Business Analyst specializing in website analysis. You are an expert in finding the right URL for a certain category. 
    You have been provided with a website and its URLs. These URLs needs to be classified to it correct categories. 
    These are categories which the provided URLs are supposed to be classified into

    1. home_page - Landing/main page URLs
    2. about_us - Company information pages
    3. terms_and_condition - Terms of service, legal pages
    4. returns_cancellation_exchange - Return/refund policy pages
    5. privacy_policy - Privacy policy pages
    6. shipping_delivery - Shipping and delivery information
    7. contact_us - Contact information and support pages
    8. catalogue - Product/service category pages
    9. urls_not_reachable - contains a list of URLs which are not reachable at the moment, meaning URL was visited sucessfully but url was not active meaning Explicit inactivity or "Non-Operation" of the website such as, containing clear indicators like " domain up for sale", "coming soon," "under construction," "website expired," or "this account has been suspended", "blank pages of domain provider", or other similar information., add it to "urls_not_reachable" key.
    10. Unreachable_via_tool - This means Gemini was not able to access the content of the URL because of some or the other reason.
    

    *** Input Data Starts ***

    1. Website: {website}
    2. dictionary of URLs of the website named "dictionary_of_URLs" --> {limited_urls_dict}
        "dictionary_of_URLs" will contain following information
        a. key will be an integer
        b. value will be the URL corresponding to this integer key
    *** Input Data Ends ***

    *** Task Starts ***
    Your job is to classify these URLs into the following categories --> ['home_page','about_us','terms_and_condition','returns_cancellation_exchange','privacy_policy','shipping_delivery','contact_us','catalogue','urls_not_reachable','Unreachable_via_tool']
    You have to extract the index corresponding to these categories.
    You will have to output just index that correspond the correct URLs.
    Please note that 1 URL can be a part of more than 1 category. Assign multiple categories only when you have gone through the content of the URL and you found that it has content of 2 or more than 2 categories. 
    *** Tasks Ends ***

    Guidelines
        1. Visit all the URLs one by one and carefully analyze each URL's content. Please scan all the text present in the URLs for assigning it to category.
        2. Please ensure that if the URLs is not reachable, DO NOT classify into any category. 
        3. Do not just look at the keywords to assign URL a category. The URL has to open and there should be some meaningful content.
        4. FAQ URLs can have almost all the information, in case there is no dedicated URL for any category, then you can consider FAQ URL for that category and assign it to the relevant categories.
        5. terms_and_conditions is to be assigned if its generic, if it has information of shipping and delivery or returns/cancellation/exchange it has to go to "shipping_delivery" and "returns_cancellation_exchange" respectively.
        6. In case a page mentions shipping policy along with terms and condition, assign this page to "shipping_delivery" and "terms_and_condition" both.
        7. In case a page mentions retuns/refunds/cancelletions along with terms and condition, assign this page to r"returns_cancellation_exchange" both.
        8. If any of the given URL was visited sucessfully but url was not active meaning Explicit inactivity or "Non-Operation" of the website such as, containing clear indicators like " domain up for sale", "coming soon," "under construction," "website expired," or "this account has been suspended", "blank pages of domain provider", or other similar information., add it to "urls_not_reachable" key.
        9. If any of the URL is not reachable via Gemini url context tool, add it to "Unreachable_via_tool" key.

    *** Important note ***
    1. ---> A URL can have content of 2 or more than 2 categories as well. Read through the entire content and then decide accordingly. Given this do not start assigning every URL to multiple categories. Assign multiple category only whn you are very sure about it and it has been clearly mentioned in the content.
    2. ---> A FAQs page containing any category related information is at "lower priority". If a dedicated URL exits then this URL should be given "high priority" for that category.

    """

        user_message = f"""
    *** Output Structure ***
    Remmember that output should indices instead of actual URLs after refering "dictionary_of_URLs".
    Try to stick 1 URL to one category. 
    The output should strictly be in the following json format containing the below 10 keys:
    {{
        "home_page": [],
        "about_us": [],
        "terms_and_condition": [],
        "returns_cancellation_exchange": [],
        "privacy_policy": [],
        "shipping_delivery": [],
        "contact_us": [],
        "catalogue": [],
        "urls_not_reachable": [],
        "Unreachable_via_tool": []
    }}

    """
        
        prompt = system + user_message
        return prompt





    





    @staticmethod
    def get_url_reachability_classification_prompt(website, url_dict):
        """Generate a prompt for classifying URLs into reachable vs unreachable for content analysis"""
        urls_list = "\n".join([f"Index {idx}: {url}" for idx, url in url_dict.items()])
        logger = ConsoleLogger(gpt_logger.analysis_id)
        logger.info(
            "Generating prompt for URL reachability classification",
            {
                "prompt_function": "get_url_reachability_classification_prompt",
                "website": website,
                "url_count": len(url_dict),
            }
        )

        prompt = f"""
        ***Task***
        You are a web accessibility expert analyzing URLs to determine their reachability for content analysis using your URL context tool. Your sole focus is on the reachability of the URL itself, not its content or explanations of its status.

        ***Input***
        URLs to analyze (provided as a dictionary of index: URL):
        `{urls_list}`

        ***Instructions***
        For each URL provided:
        1.  Utilize the URL context tool to attempt to access and process each URL.
        2.  Classify each URL into one of two categories: `reachable_urls` or `unreachable_urls`.

            **Definition of Reachability:**
            *   A URL is considered **reachable** if the URL context tool can successfully establish a connection and retrieve a response. This does not include URLs that redirect to a successfully accessed final destination. A successful retrieval generally corresponds to receiving a 2xx HTTP status code.
            *   A URL is considered **unreachable** if any of the following conditions occur:
                *   The URL context tool encounters a connection error or a timeout.
                *   The URL context tool receives a client error status code (e.g., 404 Not Found, 403 Forbidden) or a server error status code (e.g., 5xx).
                *   The URL is malformed (e.g., missing protocol, invalid characters, incorrect syntax).
                *   A redirect chain initiated by the URL fails or leads to an error state.

        **Critical Instructions**
        Return **ONLY** a valid JSON object. This object must contain exactly two arrays: `reachable_urls` and `unreachable_urls`. There should be **no** introductory text, concluding remarks, or any other content before or after the JSON object under any circumstances.

        **Important Notes:**
        *   `reachable_urls`: An array containing the original integer indices of URLs that meet the definition of reachable.
        *   `unreachable_urls`: An array containing the original integer indices of URLs that meet the definition of unreachable.
        *   If no URLs fall into a specific category, return an empty array `[]` for that category.
        *   The output must be strictly valid JSON.

        **Critical Output Format Json Structure**
        ```json
        {{
            "reachable_urls": [],
            "unreachable_urls": []
        }}
        ```
        """
        return prompt

    @staticmethod










    @staticmethod
    def summarize_website(text, image=None):

        prompt = f"""
        * Role * : You are an expert in analysing and extracting infromation from website. The information you extract will be used to classify this website into a appropiate merchant category code (referred as MCC). 

        * Task Starts *

            You will be given a website's home page text and image. You will have to extract the information such as.  
            1. If the website was live and reachable.
            2. Explicit inactivity or "Non-Operation" of the website such as, containing clear indicators like " domain up for sale", "coming soon," "under construction," "website expired," or "this account has been suspended", "blank pages of domain provider", or other similar information.
            3. If every thing is normal and site is live continue.
            4. Extract distinct types of products and services this website sells or offers. 
            5. Line of business of this website.
            6. Set of customers this website caters to.
            7. A short website description which can be very helpful for categorizing this busniess to a MCC. 

        * Task Ends *

        * Input Starts *
        You are given following things
            1. Website's Home Page Text: {text}
            2. Home page image: {image}
            It could be that either text or image might not be avaialble. Please decide accordingly.
        * Input Ends *
        
        Decision: 

        * Guidelines *
            1. Extract all the products, services, their description which are mentioned in the website to come up with the distinct types of products and services and line of business.
            2. Extract the line of business of this website by going through the text and/or screenshot provided to you.
            3. Look at the keywords in products and services first and then the images in the website for extracting the set of customers to which this website caters to, for e.g. men, women, boys, girls, children, infants, general public, pets such as dogs, cats etc. 

        * Things to keep in mind *
            1. Do not assume any information, if you are not sure about the information, do not include it in the output.
            2. Do not use your past knowledge regarding the website.
            3. If the website is "Non-Operational" based on the criteria defined in the task, stop your analysis and immediately proceed to generate the final JSON output with "N/A" and a descriptive reason.

        * Output Format *
        Return the output strictly in a JSON format with 5 keys only with no other text before or after:
        {{ 
            "non_operational": either "yes" or "no"
            "product_services": "str",
            "line_of_business": "str", 
            "customers": "str",
            "website_description": "str"
        }}
        """

        return prompt

    @staticmethod
    def get_policy_text_classification_prompt(text, website_url):
        """
        Generate a prompt for classifying extracted text into policy categories
        Used in backup flow when hard classification fails
        """

        prompt = f"""
    ***Persona***
    You are an exceptional Business Analyst and you have immense experince in analysing policy information present on websites. 
    You will have to check if the url contains any policy related information.

    *** Input Starts ***
    1. Website: {website_url}
    2. Extracted Text Content:
    {text}
    3. Image of the url
    *** Input Starts ***

    *** Tasks Starts ***
    You will be given text and occasionaly a screeshot of the URL. You will have to look at the textual data and screenshot if avaialble, to decide if the URL page is lies one or more than one of the below categories:
    1. "returns_cancellation_exchange" - Content about returns, refunds, cancellations, exchanges, warranty policies and related information
    2. "privacy_policy" - Content about privacy policies, data protection, cookie policies, user data handling and related information
    3. "shipping_delivery" - Content about shipping, delivery, fulfillment, logistics, shipping costs and related information
    4. "contact_us" - Content about contact information, customer support, help desk, contact forms and related information
    5. "terms_and_condition" - Content about terms of service, user agreements, legal terms and related information
    6. "url_not_reachable": - Explicit inactivity or "Non-Operation" of the website such as, 404 not found, containing clear indicators like " domain up for sale", "coming soon," "under construction," "website expired," or "this account has been suspended", "blank pages of domain provider", or other similar information.
    7. "other" - Content that doesn't fit into any of the above categories
    *** Tasks Ends ***

    *** Guidelines ***
    a. Focus on the content of the text, it will have clear content for the relalted categories of the URLs
    b. Look for specific keywords and phrases with context that indicate the policy type
    c. Be carefull in assiging contact_us, this is for customer help and support, and not for tracking compalains.
    d. FAQ URLs can have almost all the information, in case there is no dedicated URL for any category, then you can consider FAQ URL for that category and assign it to the relevant categories.
    e. terms_and_conditions is to be assigned if its generic, if it has information of shipping and delivery or returns/cancellation/exchange it has to go to "shipping_delivery" and "returns_cancellation_exchange" respectively.
    f. In case a page mentions shipping policy along with terms and condition, assign this page to "shipping_delivery" and "terms_and_condition" both.
    g. In case a page mentions retuns/refunds/cancelletions along with terms and condition, assign this page to r"returns_cancellation_exchange" both.

    *** Important note ***
    1. ---> A URL can have content of 2 or more than 2 categories as well. Read through the entire content and then decide accordingly. Given this do not start assigning every URL to multiple categories. Assign multiple category only whn you are very sure about it and it has been clearly mentioned in the content.
    2. ---> A FAQs page containing any category related information is at "lower priority". If a dedicated URL exits then this URL should be given "high priority" for that category.

    So please go through the text very carefully and assign appropiate categories. 
    Given this do not start assigning every URL to multiple categories. Assign multiple category only whn you are very sure about it and it has been clearly mentioned in the content.
                            

    * Output Format *
    Return your response STRICTLY in this JSON format with exactly 2 keys:
    {{
        "category": [], list of categories with very high confidence that the URL contain this information
        "reasoning": "brief explanation of why this text belongs to the selected category (max 100 words)"
    }}

    * Important Notes *
    - Output must be valid JSON only, no additional text
    - Category must be exactly from the 8 options listed in ['terms_and_condition','returns_cancellation_exchange','privacy_policy','shipping_delivery','contact_us','url_not_reachable','other']
    - Reasoning should be concise and specific to the content analyzed
    """

        return prompt