import httpx
import json
from datetime import datetime
from typing import Dict, Any

from app.config import settings
from app.database import get_session
from app.models.db_models import PolicyAnalysisNew
from app.utils.logger import ConsoleLogger


def test_webhook_endpoints(logger: ConsoleLogger) -> Dict[str, Any]:
    """
    Test both webhook endpoints to verify they are reachable and working
    
    Args:
        logger (ConsoleLogger): Logger instance
        
    Returns:
        Dict[str, Any]: Test results for both endpoints
    """
    logger.info("Starting webhook endpoints test")
    
    headers = {
        "X-API-KEY": settings.BIZTEL_API_KEY,
        "Content-Type": "application/json"
    }
    results = {
        "policy_endpoint": {"reachable": False, "error": None},
        "config": {
            "base_url": settings.BASE_URL,
            "api_key_configured": bool(settings.BIZTEL_API_KEY)
        }
    }
    

    
    # Test Policy endpoint
    policy_url = f"{settings.BASE_URL}/api/policy/results"
    logger.info(f"Testing Policy endpoint: {policy_url}")
    
    test_policy_data = {
        "scrape_request_ref_id": "webhook_test_policy_123",
        "status": "COMPLETED",
        "policy_details": {"test": True}
    }
    
    try:
        with httpx.Client(timeout=10.0) as client:
            response = client.patch(policy_url, json=test_policy_data, headers=headers)
            results["policy_endpoint"]["reachable"] = True
            results["policy_endpoint"]["status_code"] = response.status_code
            results["policy_endpoint"]["response"] = response.text
            logger.info(f"Policy endpoint test - Status: {response.status_code}")
    except Exception as e:
        results["policy_endpoint"]["error"] = str(e)
        logger.error(f"Policy endpoint test failed: {str(e)}")
    
    logger.info("Webhook endpoints test completed", data=results)
    return results


def exit_process(policy_result: Dict[str, Any], analysis_id: int, status: str, logger: ConsoleLogger):
    """
    Exit process function that updates database and sends webhook notifications with comprehensive error handling

    Args:
        policy_result (Dict[str, Any]): Policy analysis results
        analysis_id (int): Analysis ID for database updates
        status (str): Final status of the process
        logger (ConsoleLogger): Logger instance for logging
    """
    logger.info(
        "🚀 Starting exit_process function",
        {"policy_result": policy_result, "status": status, "analysis_id": analysis_id},
    )

    # Validate inputs with defaults
    try:
        if not isinstance(policy_result, dict):
            logger.warning(f"Invalid policy_result type: {type(policy_result)}, using empty dict")
            policy_result = {}
        

        
        if not analysis_id or not isinstance(analysis_id, int) or analysis_id <= 0:
            logger.error(f"Invalid analysis_id: {analysis_id}")
            return  # Cannot proceed without valid analysis_id
        
        if not status or not isinstance(status, str):
            logger.warning(f"Invalid status: {status}, defaulting to 'FAILED'")
            status = "FAILED"
            
    except Exception as validation_error:
        logger.error(f"Error validating inputs: {str(validation_error)}")
        return

    # First update the database status
    logger.info("📊 Updating database status...")
    try:
        with next(get_session()) as session:
            try:
                from app.models.db_models import PolicyAnalysisNew
                analysis = session.get(PolicyAnalysisNew, analysis_id)
                if analysis:
                    # Update policy analysis results
                    try:
                        if policy_result.get("policy_data"):
                            analysis.policy_data = json.dumps(policy_result["policy_data"])

                        # Update screenshots if available
                        if policy_result.get("screenshots"):
                            analysis.screenshots = json.dumps(policy_result["screenshots"])

                        # Update details if available
                        if policy_result.get("details"):
                            analysis.details = json.dumps(policy_result["details"])
                    except Exception as policy_update_error:
                        logger.warning(f"Error updating policy data: {str(policy_update_error)}")

                    # Update status fields with proper validation
                    try:
                        analysis.processing_status = "COMPLETED" if status == "COMPLETED" else "FAILED"

                        current_timestamp = datetime.now().isoformat() + "Z"
                        if status == "COMPLETED":
                            analysis.completed_at = current_timestamp
                        else:
                            analysis.completed_at = current_timestamp
                            if hasattr(analysis, 'error_message'):
                                analysis.error_message = f"Process failed with status: {status}"

                    except Exception as status_error:
                        logger.warning(f"Error setting status fields: {str(status_error)}")
                        # Set minimal required fields
                        analysis.processing_status = "FAILED"
                    
                    # Commit changes
                    try:
                        session.commit()
                        logger.info(
                            "✅ Database updated successfully",
                            {
                                "processing_status": getattr(analysis, 'processing_status', 'unknown'),
                                "status": status,
                                "analysis_id": analysis_id,
                            },
                        )
                    except Exception as commit_error:
                        logger.error(f"Error committing database changes: {str(commit_error)}")
                        try:
                            session.rollback()
                        except Exception as rollback_error:
                            logger.error(f"Error rolling back database changes: {str(rollback_error)}")
                        # Don't return here, still try to send webhooks
                        
                else:
                    logger.warning(f"⚠️ No analysis found with ID: {analysis_id}")
                    
            except Exception as session_error:
                logger.error(f"Error in database session operations: {str(session_error)}")
                try:
                    session.rollback()
                except Exception:
                    pass
                
    except Exception as db_error:
        logger.error("❌ Error updating analysis status in database", error=db_error)
        # Don't raise here, we still want to try sending webhooks

    # Then send webhooks using PATCH calls
    try:
        # Validate webhook configuration
        base_url = getattr(settings, 'BASE_URL', None)
        api_key = getattr(settings, 'BIZTEL_API_KEY', None)
        
        if not base_url:
            logger.error("BASE_URL not configured, cannot send webhooks")
            return
            
        if not api_key:
            logger.error("BIZTEL_API_KEY not configured, cannot send webhooks")
            return
        
        headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }
        
        # Log configuration details
        logger.info(
            "Webhook configuration",
            {
                "base_url": base_url,
                "api_key_configured": bool(api_key),
                "api_key_length": len(api_key) if api_key else 0
            }
        )
        
    except Exception as config_error:
        logger.error(f"Error validating webhook configuration: {str(config_error)}")
        return
    
    # Send Policy webhook

        try:
            logger.info("Starting Policy PATCH request...")

            with httpx.Client(timeout=30.0) as client:
                # Log request details
                logger.info(
                    "Making Policy PATCH request",
                    {
                        "url": policy_webhook_url,
                        "headers": {k: ("***HIDDEN***" if k == "X-API-KEY" else v) for k, v in policy_headers.items()},
                        "payload_size": len(str(policy_result)) if policy_result else 0,
                        "payload": policy_result  # Full payload for debugging
                    }
                )

                response = client.patch(policy_webhook_url, json=policy_result, headers=policy_headers)
                
                # Log response details
                logger.info(
                    "Policy PATCH response received",
                    {
                        "status_code": response.status_code,
                        "response_text": response.text,
                        "response_headers": dict(response.headers),
                        "url": policy_webhook_url
                    }
                )
                
                if response.status_code == 200:
                    logger.info("✅ Policy webhook sent successfully")
                else:
                    logger.error(
                        "❌ Policy webhook failed with non-200 status",
                        data={
                            "status_code": response.status_code,
                            "response": response.text,
                            "url": policy_webhook_url
                        }
                    )
                    
        except httpx.TimeoutException as e:
            logger.error(
                "❌ Policy webhook timed out",
                {
                    "url": policy_webhook_url,
                    "timeout": 30.0,
                    "error": str(e)
                }
            )
        except httpx.ConnectError as e:
            logger.error(
                "❌ Policy webhook connection failed",
                {
                    "url": policy_webhook_url,
                    "error": str(e)
                }
            )
        except Exception as e:
            logger.error(
                "❌ Policy webhook exception occurred",
                {
                    "url": policy_webhook_url,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
            
    except Exception as policy_webhook_error:
        logger.error(f"Error in Policy webhook preparation: {str(policy_webhook_error)}")

    logger.info("Exit process completed")


def send_webhook_notification(webhook_url: str, data: Dict[str, Any], webhook_type: str, logger: ConsoleLogger) -> bool:
    """
    Send a webhook notification with comprehensive logging
    
    Args:
        webhook_url (str): The webhook URL to send to
        data (Dict[str, Any]): Data to send in the webhook
        webhook_type (str): Type of webhook (for logging)
        logger (ConsoleLogger): Logger instance
        
    Returns:
        bool: True if successful, False otherwise
    """
    headers = {
        "X-API-KEY": settings.BIZTEL_API_KEY,
        "Content-Type": "application/json"
    }
    
    logger.info(
        f"Preparing {webhook_type} webhook notification",
        {
            "url": webhook_url,
            "method": "PATCH",
            "data_keys": list(data.keys()) if data else [],
            "payload_size": len(str(data)) if data else 0
        }
    )
    
    try:
        logger.info(f"Starting {webhook_type} PATCH request...")
        
        with httpx.Client(timeout=30.0) as client:
            # Log request details
            logger.info(
                f"Making {webhook_type} PATCH request",
                {
                    "url": webhook_url,
                    "headers": {k: ("***HIDDEN***" if k == "X-API-KEY" else v) for k, v in headers.items()},
                    "payload": data
                }
            )
            
            response = client.patch(webhook_url, json=data, headers=headers)
            
            # Log response details
            logger.info(
                f"{webhook_type} PATCH response received",
                {
                    "status_code": response.status_code,
                    "response_text": response.text,
                    "response_headers": dict(response.headers),
                    "url": webhook_url
                }
            )
            
            if response.status_code == 200:
                logger.info(f"✅ {webhook_type} webhook sent successfully")
                return True
            else:
                logger.error(
                    f"❌ {webhook_type} webhook failed with non-200 status",
                    data={
                        "status_code": response.status_code,
                        "response": response.text,
                        "url": webhook_url
                    }
                )
                return False
                
    except httpx.TimeoutException as e:
        logger.error(
            f"❌ {webhook_type} webhook timed out",
            {
                "url": webhook_url,
                "timeout": 30.0,
                "error": str(e)
            }
        )
        return False
    except httpx.ConnectError as e:
        logger.error(
            f"❌ {webhook_type} webhook connection failed",
            {
                "url": webhook_url,
                "error": str(e)
            }
        )
        return False
    except Exception as e:
        logger.error(
            f"❌ {webhook_type} webhook exception occurred",
            {
                "url": webhook_url,
                "error": str(e),
                "error_type": type(e).__name__
            }
        )
        return False




