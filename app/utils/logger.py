import json
import traceback
from datetime import datetime
from typing import Any, Dict, Optional, Union

from app.database import get_session
from app.models.db_models import PolicyAnalysisNew


class ConsoleLogger:
    def __init__(self, analysis_id: Union[int, str], scrape_request_id: Optional[str] = None):
        self.analysis_id = analysis_id
        self.scrape_request_id = scrape_request_id
        
        # Only try to get from database if analysis_id is an integer
        if isinstance(analysis_id, int) and not self.scrape_request_id:
            try:
                with next(get_session()) as session:
                    analysis = session.get(PolicyAnalysisNew, analysis_id)
                    if analysis:
                        self.scrape_request_id = analysis.scrape_request_ref_id
            except Exception:
                pass

    def _format_message(
        self, level: str, message: str, data: Optional[Dict[str, Any]] = None
    ) -> str:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        prefix = f"[{timestamp}][{self.analysis_id}][{self.scrape_request_id or 'NO_REF'}]"
        if data:
            try:
                data_str = "\n" + json.dumps(data, indent=2, default=str)
            except:
                data_str = f"\n{str(data)}"
        else:
            data_str = ""
        return f"{prefix} {level}: {message}{data_str}"

    def info(self, message: str, data: Optional[Dict[str, Any]] = None):
        print(self._format_message("INFO", message, data))

    def error(
        self, message: str, error: Optional[Exception] = None, data: Optional[Dict[str, Any]] = None
    ):
        # First print the error message with data
        error_data = data or {}
        if error:
            error_data["error"] = str(error)
        print(self._format_message("ERROR", message, error_data))

        # Then print the traceback in its original format
        try:
            if error:
                print(f"Traceback for analysis {self.analysis_id}:")
                traceback.print_exception(type(error), error, error.__traceback__)
            else:
                # Skip traceback printing when no exception is provided
                # This prevents issues in the Celery worker environment
                pass
        except Exception as tb_error:
            print(f"Error printing traceback: {str(tb_error)}")

    def debug(self, message: str, data: Optional[Dict[str, Any]] = None):
        print(self._format_message("DEBUG", message, data))

    def warning(self, message: str, data: Optional[Dict[str, Any]] = None):
        print(self._format_message("WARNING", message, data))

    def critical(self, message: str, data: Optional[Dict[str, Any]] = None):
        print(self._format_message("CRITICAL", message, data))
